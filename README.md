# Deliveryman.ai React Frontend

### Setup Requirement:
- Node v22.2.0

NOTE: Please use [pnpm](https://pnpm.io/) instead of npm for installing packages and running server.

### Setup:
1. Use `.env.example` to create and set up `.env` file in root of project.
2. Run `pnpm install`

### Start:

Run `pnpm run start`

### Tunnel:

You can use [localtunnel](https://theboroer.github.io/localtunnel-www/) to start and share the tunnel link. You will also need this for running secure (wss://) websockets and setting up stripe webhooks.

Start tunnel using `lt --port 3000 --subdomain <subdomain>`

Use below format for subdomain: \
**<your_first_name>-deliveryman-app**

**NOTE**: If you want to share the tunnel link with someone, you'll need to use your tunnel urls instead of localhost for REACT_APP_BACKEND, REACT_APP_FRONTEND and REACT_APP_WS_HOST.
