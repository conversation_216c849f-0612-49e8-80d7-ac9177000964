const path = require('path');
module.exports = {
  webpack: {
    alias: {
      '@components': path.resolve(__dirname, 'src', 'components'),
      '@lib': path.resolve(__dirname, 'src', 'lib'),
      '@assets': path.resolve(__dirname, 'src', 'assets'),
      '@images': path.resolve(__dirname, 'src', 'assets', 'images'),
      '@admin': path.resolve(__dirname, 'src', 'admin'),
      '@pages': path.resolve(__dirname, 'src', 'pages'),
      '@routes': path.resolve(__dirname, 'src', 'router.tsx'),
    },
  },
};
