{"name": "coldemailer-frontend", "version": "0.1.0", "private": true, "dependencies": {"@calcom/embed-react": "^1.5.3", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/roboto": "^5.1.0", "@mui/icons-material": "^6.1.5", "@mui/material": "^6.1.5", "@mui/x-data-grid": "^7.22.1", "@mui/x-date-pickers": "^7.24.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tanstack/react-query": "^5.59.16", "@tawk.to/tawk-messenger-react": "^2.0.2", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@toolpad/core": "^0.8.1", "@types/jest": "^27.5.2", "@types/node": "^16.18.115", "@types/papaparse": "^5.3.15", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "ag-grid-community": "^33.2.4", "ag-grid-react": "^33.2.4", "aieditor": "^1.3.3", "axios": "^1.7.7", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "fast-deep-equal": "^3.1.3", "ldrs": "^1.0.2", "luxon": "^3.5.0", "notistack": "^3.0.1", "papaparse": "^5.4.1", "react": "^18.3.1", "react-calendly": "^4.3.1", "react-dom": "^18.3.1", "react-quill": "^2.0.0", "react-router-dom": "^6.27.0", "react-scripts": "5.0.1", "socket.io-client": "^4.8.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "craco eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "packageManager": "pnpm@9.12.2+sha512.22721b3a11f81661ae1ec68ce1a7b879425a1ca5b991c975b074ac220b187ce56c708fe5db69f4c962c989452eee76c82877f4ee80f474cebd61ee13461b6228", "devDependencies": {"@craco/craco": "^7.1.0", "@testing-library/jest-dom": "^5.17.0", "@types/luxon": "^3.4.2", "@types/testing-library__jest-dom": "*"}}