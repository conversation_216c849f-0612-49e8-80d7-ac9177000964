import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>po<PERSON>} from "@mui/material";
import {useNavigate, useSearchParams} from "react-router-dom";
import {urls} from "@routes";
import {useState} from "react";
import {useMutation} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndPostData, retryFn} from "@lib/apis";
import {useSnackbar} from "notistack";


export default function ChangePassword() {
	const navigate = useNavigate();
	const [searchParams] = useSearchParams();
	const {enqueueSnackbar} = useSnackbar();

	const [password, setPassword] = useState<string>("");

	const changePasswordMutation = useMutation({
		mutationKey: ['changePassword', password],
		mutationFn: () => authenticateAndPostData(
			"/auth/reset-password/change-password/",
			{
				"token": searchParams.get("token"),
				"new_password": password,
			},
		),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			enqueueSnackbar(
				"Success! Please login with the new password.",
				{
					variant: "success",
				});
			navigate(urls["login"]);
		},
		onError: (error: ApiRequestFailed) => {
			// Show error message.
			console.error(error)
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	function handleChangePassword() {
		changePasswordMutation.mutate();
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<Container maxWidth={"xs"}>
			<Stack direction="column" spacing={1}>
				<Typography variant="h3" align={"center"}>Change Password</Typography>
				<Typography variant="h5" align={"center"}>Please enter your new password.</Typography>
			</Stack>
			<Stack direction="column" spacing={2} marginTop={5}>
				<TextField type="password"
									 id="password"
									 label="New Password"
									 variant="outlined"
									 value={password}
									 onChange={e => setPassword(e.target.value)}
									 required/>
			</Stack>
			<Stack direction="column" spacing={2} marginTop={5}>
				<Button variant="contained" color="primary" type="submit" onClick={handleChangePassword}
								disabled={changePasswordMutation.isPending}>
					Confirm
				</Button>
			</Stack>
		</Container>
	)
}
