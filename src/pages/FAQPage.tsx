import {Box, Container, Divider, Stack, Typography} from "@mui/material";
import {useEffect, useState} from "react";
import PageLoading from "@components/PageLoading";
import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {useQuery} from "@tanstack/react-query";
import CustomizedAccordion from "@components/CustomizedAccordion";


interface PageData {
	status_code: string
	status_text: string
}


export default function FAQPage() {
	const [pageData, setPageData] = useState<PageData>();

	// Page Title
	useEffect(() => {
    document.title = "FAQ - Deliveryman.ai";
  }, []);

	// Fetch the page data
	const pageDataQuery = useQuery({
		queryKey: ["dashboard"],
		queryFn: () => authenticateAndFetchData("/dashboard/"),
		refetchOnWindowFocus: false,
		retry: retryFn,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data as PageData);
		}
	}, [pageDataQuery.data]);

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Container sx={{pb: 6}}>
				<Stack direction={"column"} spacing={4}>
					<Typography variant={"h4"} align={"center"}>Frequently Asked Questions</Typography>
					<Divider/>
					<Stack direction={"column"} spacing={0}>
						<CustomizedAccordion heading={"How does DeliverymanAI improve email deliverability?"}
																 content={"DeliverymanAI handles everything needed to maximize inbox placement from " +
																	 "lead email verification, multiple email creation, inbox warm-up, domain reputation " +
																	 "building, custom sending schedules, bounce detection, global blacklist filters, " +
																	 "and spam trigger analysis, all automated for you."}
						/>
						<CustomizedAccordion heading={"Do I need to set up or warm up multiple inboxes?"}
																 content={"Nope. You bring one root domain, and we handle the subdomain creation, " +
																	 "email creations, inbox setup, warm-up, and health monitoring for you at no " +
																	 "additional cost."}
						/>
						<CustomizedAccordion heading={"How many emails can I send per day?"}
																 content={"Depending on your domain age, the system will scale from hundreds to " +
																	 "thousands of emails per day, spread intelligently across multiple inboxes to " +
																	 "avoid spam filters. We are constantly optimizing this limit based on our " +
																	 "internal and user testing."}
						/>
						<CustomizedAccordion heading={"Can I connect my own domains or do I use DeliverymanAI’s?"}
																 content={"You can connect your own root domain. We’ll handle all subdomain + inbox " +
																	 "creation from there. You maintain control, we manage the complexity."}
						/>
						<CustomizedAccordion heading={"Does it support Gmail, Outlook, or custom SMTP?"}
																 content={"We don’t use Gmail or Outlook. Our system is optimized for custom SMTP " +
																	 "with dedicated IP rotation and advanced warm-up logic, much better for " +
																	 "targeted outreach."}
						/>
						<CustomizedAccordion
							heading={"How is this different from traditional warm-up tools like Lemwarm or Mailreach?"}
							content={
								<Box>
									Those tools only warm up existing inboxes. We go beyond that:
									<ul>
										<li>We create, manage, and warm up inboxes at scale</li>
										<li>We rotate sending across many inboxes</li>
										<li>We auto-replace inboxes that are blacklisted or flagged.</li>
									</ul>
									It's a complete cold email infrastructure and not just warm-up.
								</Box>
							}
						/>
						<CustomizedAccordion heading={"How do I set up my first cold email campaign?"}
																 content={
							<Box>
								It’s simple:
								<ul>
									<li>Add your domain</li>
									<li>Upload your contact list</li>
									<li>Add your email sequence</li>
									<li>We handle everything else: subdomains, inboxes, warm-up, sending, and tracking.</li>
								</ul>
							</Box>
						}
						/>
						<CustomizedAccordion heading={"Can I personalize emails at scale?"}
																 content={"Yes. We use formats like {{first_name}}, {{company}}, etc. You can " +
																	 "conditionally include any column from your uploaded sheet and change parts " +
																	 "of the message based on your lead data."}
						/>
						<CustomizedAccordion heading={"How do I know if my emails are landing in inbox or spam?"}
																 content={"We run regular seed tests across major providers " +
																	 "(Gmail, Outlook, Yahoo, etc.) to track your placement and alert you if " +
																	 "issues arise."}
						/>
						<CustomizedAccordion heading={"What’s the typical response rate users get?"}
																 content={"Response rates vary by targeting and message quality, but many users " +
																	 "see 5-8% reply rates with well-written, relevant emails."}
						/>
						<CustomizedAccordion heading={"Do I need to bring my own domains?"}
																 content={"Yes, you need to bring your own root domain. We do the rest. " +
																	 "Full control, zero hassle."}
						/>
						<CustomizedAccordion heading={"Can I buy domains directly through DeliveryMan.ai?"}
																 content={"Coming soon! Domain purchasing and setup will be available directly " +
																	 "inside the dashboard."}
						/>
						<CustomizedAccordion heading={"Do you rotate inboxes automatically?"}
																 content={"Yes. Each campaign is split across many inboxes. Rotation happens " +
																	 "automatically even mid-campaign to maintain high deliverability."}
						/>
						<CustomizedAccordion heading={"Can I connect multiple domains to one campaign?"}
																 content={"Yes. You can run a campaign across multiple domains and inboxes for " +
																	 "volume and deliverability scaling."}
						/>
						<CustomizedAccordion heading={"Can I get flagged or banned for spam using your tool?"}
																 content={"If you follow best practices, no. Our system is designed to avoid spam " +
																	 "flags. But if you send poor-quality, irrelevant emails at high volume, any " +
																	 "system can be flagged. We help you stay safe."}
						/>
						<CustomizedAccordion heading={"What’s the best email sending volume to stay safe?"}
																 content={"Start slow and scale. We recommend 25-100 emails/day/domain initially " +
																	 "depending on your domain age. This limit keeps increasing overtime based on how " +
																	 "users are engaging with your emailers. Our platform handles this scaling for you."}
						/>
						<CustomizedAccordion heading={"How should I structure my cold email sequence?"}
																 content={
							<Box>
								We suggest:
								<ul>
									<li>1 intro email</li>
									<li>2-3 short follow-ups</li>
									<li>Breaks of 2-3 days between touches</li>
								</ul>
								Keep it short, relevant, and value-driven.
							</Box>
						}
						/>
					</Stack>
				</Stack>
			</Container>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
