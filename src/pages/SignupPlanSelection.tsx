import {useEffect, useState} from "react";
import {useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {Box, Container, Grid2, Stack, Switch, Typography, useTheme} from "@mui/material";
import PricingPlanCard from "@components/PricingPlanCard";
import white_logo from "@assets/branding/coldemailer_white_full_logo.svg";
import black_logo from "@assets/branding/coldemailer_black_full_logo.svg";

interface PageData {
	status_code: string
	status_text: string

	plans: SubscriptionPlan[]
}

interface SubscriptionPlan {
	id: number
	name: string
	monthly_amount: number
	annual_amount: number
	popular: boolean
	monthly_feature_list: string[]
	annual_feature_list: string[]
}


export default function SignupPlanSelection() {
	const theme = useTheme();

	const [pageData, setPageData] = useState<PageData>();
	const [annual, setAnnual] = useState(false);

	// Query to fetch page data.
	const pageDataQuery = useQuery({
		queryKey: ["onboardingPlanSelection"],
		queryFn: () => authenticateAndFetchData("/onboarding/plan-selection/"),
		gcTime: 0,
		refetchOnWindowFocus: false,
		retry: retryFn,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data as PageData);
		}
	}, [pageDataQuery.data]);

	function BrandingLogo() {
		const isDarkTheme = useTheme().palette.mode === 'dark'

		if (isDarkTheme) {
			return (
				<Box display={"flex"} justifyContent={"center"} alignItems={"center"}>
					<img src={white_logo} alt={"logo"} style={{width: "18em", height: "auto"}}/>
				</Box>
			)
		} else {
			return (
				<Box display={"flex"} justifyContent={"center"} alignItems={"center"}>
					<img src={black_logo} alt={"logo"} style={{width: "18em", height: "auto"}}/>
				</Box>
			)
		}
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Box sx={{wdith: "100%", height: "100%", backgroundColor: theme.palette.background.default}}>
				<Container maxWidth={"xl"} sx={{mt: 6}}>
					<BrandingLogo/>
					<Stack direction={"column"} spacing={1} sx={{width: "100%", mt: 4}}>
						<Typography variant={"h4"} fontWeight={"bold"} align={"center"} color={"primary"}>
							Select a Pricing Plan
						</Typography>
						<Typography variant={"body1"} align={"center"}>
							50% OFF for the first month. Limited Time Only.
						</Typography>
						{/* Plan Cards */}
						<Stack direction={"column"} alignItems={"center"} justifyContent={"center"} spacing={4}
									 sx={{width: "100%", mt: 4}}>
							<Stack direction={"row"} spacing={1} sx={{alignItems: 'center'}}>
								<Typography>Monthly</Typography>
								<Switch
									checked={annual}
									onChange={e => {
										setAnnual(e.target.checked);
									}}
								/>
								<Typography>Annual</Typography>
							</Stack>
							<Grid2 container spacing={3} justifyContent={"center"} sx={{width: "100%"}}>
								{pageData.plans.map(plan => (
									<Grid2 size={{xs: 12, sm: 12, md: 3}} key={plan.id}>
										<PricingPlanCard planId={plan.id}
																		 planName={plan.name}
																		 annualPrice={plan.annual_amount}
																		 monthlyPrice={plan.monthly_amount}
																		 isMonthly={!annual}
																		 monthlyFeatureList={plan.monthly_feature_list}
																		 annualFeatureList={plan.annual_feature_list}
																		 isPopular={plan.popular}
																		 currentPlan={false}
																		 paidUser={false}/>
									</Grid2>
								))}
							</Grid2>
						</Stack>
					</Stack>
				</Container>
			</Box>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
