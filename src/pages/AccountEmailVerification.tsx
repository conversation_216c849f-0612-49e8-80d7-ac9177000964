import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import {Box, Stack, Typography, Container, Button} from "@mui/material";
import {useEffect, useState} from "react";
import {useQuery} from "@tanstack/react-query";
import {useParams, useNavigate} from "react-router-dom";
import {urls} from "@routes";
import PageLoading from "@components/PageLoading";
import { BrandingLogo } from "@pages/Base"

export default function AccountEmailVerification() {
	// --------------------- QUERY PARAMETERS ---------------------
	const { token } = useParams();
    const navigate = useNavigate();

	const emailVerifyQuery = useQuery({
        queryKey: ["emailVerifyQuery"],
        queryFn: () => authenticateAndFetchData(`/verify-email/?token=${token}`),
        gcTime: 0,
        retry: retryFn,
        refetchOnWindowFocus: false,
    });
	
    useEffect(()=>{
        if(!emailVerifyQuery.isLoading && !emailVerifyQuery.error){
            sessionStorage.setItem("user-email-verified", "true");
        }
    }, [emailVerifyQuery.isLoading, emailVerifyQuery.error])

    if (emailVerifyQuery.isLoading || emailVerifyQuery.isRefetching) {
        return (
            <PageLoading/>
        )
    } else if (emailVerifyQuery.error as unknown as ApiRequestFailed) {
        return (
            <>
            <Stack spacing={3} alignItems="center">
                <BrandingLogo />
                <Typography variant="h4" color="primary">Link Expired. Resend the Verification Email from settings.</Typography>                
            </Stack>
            </>
        )

    } else {
        return(
           <Container maxWidth="sm">
                <Box sx={{ mt: 12, textAlign: "center" }}>
                    <Stack spacing={3} alignItems="center">
                         <BrandingLogo />
                        <Typography variant="h4" color="primary">
                            Email Verified Successfully!
                        </Typography>
                        <Typography variant="body1">
                            You’ve successfully verified your email address. All features are now unlocked — you can now connect domains, create campaigns, and more.
                        </Typography>
                        <Button
                            variant="contained"
                            color="primary"
                            onClick={() => navigate(urls["dashboard"])}
                            sx={{ mt: 2, px: 4 }}
                        >
                            Go to Dashboard
                        </Button>
                    </Stack>
                </Box>
            </Container>

        )
    }
}
