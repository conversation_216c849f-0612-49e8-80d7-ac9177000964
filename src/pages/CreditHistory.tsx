import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import {MenuItem, Select, Button, Avatar, Box, Card, CardContent, Grid2, Paper, Stack, Typography, useTheme, Container, Chip} from "@mui/material";
import {useEffect, useState} from "react";
import {useSnackbar} from "notistack";
import {useQuery, useMutation} from "@tanstack/react-query";
import {ColDef, ValueFormatterParams} from "ag-grid-community";
import ServerSideDataTable from "@components/ServerSideDataTable";
import {Link} from "react-router-dom";
import {urls} from "@routes";
import {CustomCellRendererProps} from "ag-grid-react";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {format} from "date-fns";
import { formatInTimeZone } from "date-fns-tz";

interface CreditHistoryData {
    campaign_uid: string
    campaign_schedule_uid: string
    campaign_name: string
    receiver: string
    sender: string
    time_of_email_sent: number
    status: string
}

export default function CreditHistory() {    
    const theme = useTheme();

    const [pageData, setPageData] = useState<CreditHistoryData[]>([]);
    const [timezone, setTimezone] = useState("")
    
    // Page Title
    useEffect(() => {
    document.title = "Sending History - Deliveryman.ai";
  }, []);

    // Fetch the page data
    const pageDataQuery = useQuery({
        queryKey: ["SendingHistoryQuery"],
        queryFn: () => authenticateAndFetchData("/credit-history/"),
        refetchOnWindowFocus: false,
        retry: retryFn,
    });
    useEffect(() => {
        if (pageDataQuery.data) {
            setPageData(pageDataQuery.data.data.data);
            setTimezone(pageDataQuery.data.data.timezone)
        }
    }, [pageDataQuery.data]);

    const StatusBadgeCell = (params: CustomCellRendererProps) => {
        const value = params.value;

        const badgeProps = {
            size: "small" as const,
            sx: { fontSize: "0.75rem", height: 24 },
        };

        switch (value) {
            case "Success":
                return <Chip {...badgeProps} color="success" label="Success" />;
            case "Positive":
                return <Chip {...badgeProps} color="primary" label="Positive" />;
            case "Negative":
            case "Failed":
                return <Chip {...badgeProps} color="error" label={value} />;
            case "Bounced":
                return <Chip {...badgeProps} color="warning" label="Bounced" />;
            case "Unsubscribed":
                return <Chip {...badgeProps} color="secondary" label="Unsubscribed" />;
            case "Resubscribe":
                return <Chip {...badgeProps} sx={{backgroundColor: "#20c997", color: "white"}} label="Resubscribe" />;
            case "Neutral":
                return <Chip {...badgeProps} color="default" label="Neutral" />;
            case "Replied":
                return <Chip {...badgeProps} color="info" label="Replied" />;
            case "":
                return <Chip {...badgeProps} color="error" label="N/A" />;
            default:
                return <Chip {...badgeProps} color="default" label={value || "Unknown"} />;
        }
    };

    function timestampFormatter(params: ValueFormatterParams, timezone: string) {
        if (!params.value) return "";

        return formatInTimeZone(
            new Date(params.value),
            timezone,
            "hh:mm a, do MMM yyyy"
        ).replace(/\b(AM|PM)\b/, (match) => match.toLowerCase());
    }
    
    const CampaignLink = (params: CustomCellRendererProps) => {        
        return (
            <Link style={{color: theme.palette.text.primary}}
                        to={urls["campaignEmailDetails"].replace(":campaignUID", params.data["campaign_uid"])}
                        state={{ from: "SendingHistory" }}
                        >
                {params.value}
            </Link>
        )
    }
    

    const columnDefs: ColDef[] = [
        {field: "campaign_name", headerName: "Campaign Name", cellRenderer: CampaignLink},
        {field: "receiver", headerName: "Receiver"},
        {field: "sender", headerName: "Sender"},
        {field: "time_of_email_sent", headerName: "Email Sent On", valueFormatter: (params) => timestampFormatter(params, timezone), flex: 2},            
        {field: "status", headerName: "Status", cellRenderer: StatusBadgeCell, sortable: false},            
    ]

    if (pageDataQuery.isLoading || pageDataQuery.isRefetching) {
        return (
            <PageLoading/>
        )

    } else if (pageDataQuery.error as unknown as ApiRequestFailed) {
        return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

    } else {
        return(
            <Stack direction={"column"} spacing={4} sx={{pb: 4, height: "100%"}}>
                            <Stack direction={"column"} spacing={1}>
                                <Typography variant={"h4"} align={"center"} color={"primary"}>
                                    Sending History
                                </Typography>
                            </Stack>
                            <Box sx={{height: "100%"}}>
                                <ServerSideDataTable columns={columnDefs}
                                                                        rows={pageData}                                                                     
                                                                        noRowsText={"No Credit History Available"}
                                                                        loading={pageDataQuery.isFetching || pageDataQuery.isLoading }/>
                            </Box>                
                        </Stack>
        )
    }
}
