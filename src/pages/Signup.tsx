import {<PERSON>, <PERSON><PERSON>, Con<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typo<PERSON>, useTheme} from "@mui/material";
import {Link, useNavigate, useSearchParams} from "react-router-dom";
import {urls} from "@routes";
import {useState} from "react";
import {useMutation} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndPostData} from "@lib/apis";
import {saveToken} from "@lib/jwt";
import {useSnackbar} from "notistack";
import white_logo from "@assets/branding/coldemailer_white_full_logo.svg";
import black_logo from "@assets/branding/coldemailer_black_full_logo.svg";


interface SignupSuccessData {
	status_code: number
	status_text: string
	access_token: string
	refresh_token: string
	session_url: string | null
}


function BrandingLogo() {
	const isDarkTheme = useTheme().palette.mode === 'dark'

	if (isDarkTheme) {
		return (
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"}>
				<img src={white_logo} alt={"logo"} style={{width: "18em", height: "auto"}}/>
			</Box>
		)
	} else {
		return (
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"}>
				<img src={black_logo} alt={"logo"} style={{width: "18em", height: "auto"}}/>
			</Box>
		)
	}
}


export default function Signup() {
	const navigate = useNavigate();
	const {enqueueSnackbar} = useSnackbar();
	const [searchParams] = useSearchParams();

	const [username, setUsername] = useState<string>("");
	const [email, setEmail] = useState<string>("");
	const [password, setPassword] = useState<string>("");

	const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

	const signupMutation = useMutation({
		mutationKey: ['signup', username, email, password],
		mutationFn: () => authenticateAndPostData(
			"/auth/signup/",
			{
				"username": username,
				"email": email,
				"password": password,
				"price_id": searchParams.get("price_id") || null,
				"affiliate_id": window["affiliateId"],
				"timezone": timezone
			}
		),
		gcTime: 0,
		onSuccess: (response) => {
			// Set tokens & send user to dashboard page.
			let data: SignupSuccessData = response.data;
			if (data.access_token && data.refresh_token) {
				saveToken("access", data.access_token);
				saveToken("refresh", data.refresh_token);

				// Start checkout session if present. Otherwise navigate to mandatory plan selection page.
				if (data.session_url) {
					window.location.href = data.session_url;
				} else {
					navigate(urls["signupPlanSelection"]);
				}
			} else {
				// Ideally should not happen. But just in case.
				console.error("Oops, Something went wrong on our end! Please try again.");
				enqueueSnackbar("Oops, Something went wrong on our end! Please try again.", {
					variant: "error",
				});
			}
		},
		onError: (error: ApiRequestFailed) => {
			// Show error message.
			console.error(error.data.message);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	function handleSignup() {
		if (username && email && password) {
			signupMutation.mutate();
		} else {
			console.error("Missing username / password / email")
			enqueueSnackbar("Missing username / password / email", {
				variant: "error",
			});
		}
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<Container maxWidth={"xs"}>
			<BrandingLogo/>
			<Stack direction="column" spacing={1} sx={{mt: 6}}>
				<Typography variant="h3" align={"center"}>Sign Up</Typography>
				<Typography variant="h5" align={"center"}>Welcome! Create a new Account.</Typography>
			</Stack>
			<form
				onSubmit={(e) => {
					e.preventDefault();
					handleSignup();
				}}
			>
			<Stack direction="column" spacing={2} marginTop={5}>
				<TextField type="text"
									 id="username"
									 label="Full Name"
									 variant="outlined"
									 value={username}
									 onChange={e => setUsername(e.target.value)} required/>
				<TextField type="email"
									 id="email"
									 label="Email ID"
									 variant="outlined"
									 value={email}
									 onChange={e => setEmail(e.target.value)} required/>
				<TextField type="password"
									 id="password"
									 label="Password"
									 variant="outlined"
									 value={password}
									 onChange={e => setPassword(e.target.value)} required/>
			</Stack>
			<Stack direction="column" spacing={2} marginTop={5}>
				<Button variant="contained" color="primary" type="submit"
								disabled={signupMutation.isPending}>
					Sign Up
				</Button>
				<Typography variant="body2" align={"center"}>
					Already have an account? <Link to={urls["login"]}>Log In</Link>.
				</Typography>
			</Stack>
		</form>
		</Container>
	)
}
