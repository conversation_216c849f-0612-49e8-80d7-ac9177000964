import {Box, CssB<PERSON>line, ThemeProvider, Typography} from "@mui/material";
import {createTheme} from "@mui/material/styles";
import {useSearchParams} from "react-router-dom";
import {useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, fetchData, retryFn} from "@lib/apis";
import PageLoading from "@components/PageLoading";

export default function UnsubscribeEmail() {
	const darkTheme = createTheme({
		palette: {
			mode: "dark",
		},
	});

	const [searchParams] = useSearchParams();

	// Make query to unsubscribe this email.
	const unsubscribeQuery = useQuery({
		queryKey: ["unsubscribe"],
		queryFn: () => fetchData(
			`/campaigns/unsubscribe/?cid=${searchParams.get("cid") || ""}&email=${searchParams.get("email") || ""}`
		),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (unsubscribeQuery.isLoading) {
		return (
			<ThemeProvider theme={darkTheme}>
				<CssBaseline/>
				<Box display={"flex"}
						 flexDirection={"column"}
						 justifyContent={"center"}
						 alignItems={"center"}
						 className={"dark-mode"}
						 sx={{width: "100vw", height: "100vh"}}>
					<PageLoading/>
				</Box>
			</ThemeProvider>
		)

	} else if (unsubscribeQuery.error as unknown as ApiRequestFailed) {
		return (
			<ThemeProvider theme={darkTheme}>
				<CssBaseline/>
				<Box display={"flex"}
						 flexDirection={"column"}
						 justifyContent={"center"}
						 alignItems={"center"}
						 className={"dark-mode"}
						 sx={{width: "100vw", height: "100vh"}}>
					<Typography variant={"h4"}>Oops! Something went wrong. Please try again.</Typography>
				</Box>
			</ThemeProvider>
		)

	} else {
		return (
			<ThemeProvider theme={darkTheme}>
				<CssBaseline/>
				<Box display={"flex"}
						 flexDirection={"column"}
						 justifyContent={"center"}
						 alignItems={"center"}
						 className={"dark-mode"}
						 sx={{width: "100vw", height: "100vh"}}>
					<Typography variant={"h4"}><b>{searchParams.get("email") || ""}</b> has been unsubscribed.</Typography>
				</Box>
			</ThemeProvider>
		)
	}
}
