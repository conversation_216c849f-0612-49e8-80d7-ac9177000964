import coldEmailerTheme from "@assets/themes/coldEmailerTheme";
import {SnackbarProvider} from "notistack";
import {Box, ThemeProvider} from "@mui/material";
import {Outlet} from "react-router-dom";

export default function OnboardingBase() {
	return (
		<ThemeProvider theme={coldEmailerTheme}>
			<SnackbarProvider>
				<Box display="flex" flexDirection="column" sx={{height: "100vh"}}>
					<Outlet/>
				</Box>
			</SnackbarProvider>
		</ThemeProvider>
	)
}
