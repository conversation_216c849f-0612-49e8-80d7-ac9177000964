import {
	Button,
	<PERSON>alog,
	<PERSON>alogActions,
	DialogContent,
	DialogContentText,
	DialogTitle,
	FormControl,
	InputLabel,
	MenuItem,
	Paper,
	Select,
	Stack,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Toolbar,
	Tooltip,
	Typography
} from "@mui/material";
import googleIcon from "@assets/images/google_g_logo.svg";
import * as React from "react";
import {useEffect, useState} from "react";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {Add, Delete} from "@mui/icons-material";
import {useSnackbar} from "notistack";
import { format } from "date-fns";

interface PageData {
	status_code: string
	status_text: string

	domains: PostmasterDomain[]
	available_managed_domains: string[]
}

interface PostmasterDomain {
	domain: string
	added_on_ts: number
}

export default function Postmaster() {
	const {enqueueSnackbar} = useSnackbar();

	const [pageData, setPageData] = useState<PageData>();
	const [
		openAddNewDomainDialog,
		setOpenAddNewDomainDialog
	] = useState<boolean>(false);

	// Fetch the page data
	const pageDataQuery = useQuery({
		queryKey: ["googlePostmaster"],
		queryFn: () => authenticateAndFetchData("/integrations/google-postmaster/"),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			console.log(pageDataQuery.data.data);
			setPageData(pageDataQuery.data.data as PageData);
		}
	}, [pageDataQuery.data]);

	function closeAddNewDomainDialog() {
		setOpenAddNewDomainDialog(false);
	}

	/**
	 * Callback for when a new domain has been successfulyl connected with Google Postmaster.
	 * @param domain - ex. draftss.com
	 */
	function addNewDomainSuccessCallback(domain: string) {
		closeAddNewDomainDialog();
		enqueueSnackbar(`Domain ${domain} has been added successfully! 
		Please complete any remaining steps on Google Postmaster dashboard.`, {
			variant: "success",
		});
		pageDataQuery.refetch().then();
	}

	/**
	 * Callback for when add new domain fails.
	 * @param message - Error message to show in toast.
	 */
	function addNewDomainFailCallback(message: string) {
		enqueueSnackbar(message, {variant: "error"});
	}

	/**
	 * Callback for when a domain has been removed successfully from GOogle Postmaster integration.
	 */
	function removeDomainSuccessCallback(domain: string) {
		enqueueSnackbar(`Domain ${domain} has been removed from Google Postmaster integration.`, {
			variant: "success",
		});
		pageDataQuery.refetch().then();
	}

	/**
	 * Callback for when remove domain fails.
	 * @param message - Error message to show in toast.
	 */
	function removeDomainFailCallback(message: string) {
		enqueueSnackbar(message, {variant: "error"});
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={4} justifyContent={"center"} alignItems={"center"}>
				<Stack direction={"row"} alignItems={"center"} justifyContent={"center"}>
					<img src={googleIcon} alt={"Google Icon"} style={{width: "3rem", height: "3rem", marginRight: "1em"}}/>
					<Typography variant="h3" align={"center"} color={"primary"} textAlign={"center"}>
						Google Postmaster
					</Typography>
				</Stack>

				<div style={{display: "grid", width: "100%", marginBottom: "6em"}}>
					<Toolbar sx={{justifyContent: "end"}} disableGutters>
						<Button startIcon={<Add/>} variant="contained" color="primary"
										onClick={() => setOpenAddNewDomainDialog(true)}>
							Add New Domain
						</Button>
					</Toolbar>
					<TableContainer component={Paper} sx={{overflow: "auto"}}>
						<Table sx={{minWidth: 650}} aria-label="google postmaster verified domains">
							<TableHead>
								<TableRow>
									<TableCell>Domain</TableCell>
									<TableCell align="center">Added On</TableCell>
									<TableCell align="right">Remove Domain</TableCell>
								</TableRow>
							</TableHead>
							<TableBody>
								{pageData.domains.length === 0 &&
                    <TableRow sx={{'&:last-child td, &:last-child th': {border: 0}}}>
                        <TableCell colSpan={7} align={"center"}>
                            No domains available.
                        </TableCell>
                    </TableRow>}
								{pageData.domains.map(verifiedDomain => (
									<TableRow
										key={`${verifiedDomain.domain}-${verifiedDomain.added_on_ts}`}
										sx={{'&:last-child td, &:last-child th': {border: 0}}}
									>
										<TableCell component="th" scope="row">{verifiedDomain.domain}</TableCell>
										<TableCell align="center">
											{format(new Date(verifiedDomain.added_on_ts), "do MMM y, HH:mm:ss (xxx)")}
										</TableCell>
										<TableCell align="right">
											<Stack direction={"row"} justifyContent={"end"} spacing={1}>
												{/* Delete */}
												<Tooltip title={"Remove this domain from list."}>
													<DeleteDomainButton domain={verifiedDomain.domain}
																							successCallback={removeDomainSuccessCallback}
																							failCallback={removeDomainFailCallback}/>
												</Tooltip>
											</Stack>
										</TableCell>
									</TableRow>
								))}
							</TableBody>
						</Table>
					</TableContainer>
				</div>
				<AddNewDomainDialog availableDomains={pageData.available_managed_domains}
														open={openAddNewDomainDialog}
														close={closeAddNewDomainDialog}
														successCallback={addNewDomainSuccessCallback}
														failCallback={addNewDomainFailCallback}/>
			</Stack>
		)

	} else {
		// Ideally it should not reach here.
		return <></>;
	}
}


/**
 * Shows dialog to add add new domain to postmaster integration.
 */
function AddNewDomainDialog(props: {
	availableDomains: string[],
	open: boolean,
	close: () => void,
	successCallback: (domain: string) => void,
	failCallback: (message: string) => void,
}) {
	const [selectedDomain, setSelectedDomain] = useState<string>("");
	const [txtValue, setTxtValue] = useState<string>("");

	// Mutation to add new domain to google postmaster.
	const addDomainMutation = useMutation({
		mutationKey: ["addNewPostmasterDomain"],
		mutationFn: () => authenticateAndPostData("/integrations/google-postmaster/add/", {
			"domain": selectedDomain,
			"TXT": txtValue,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			props.successCallback(selectedDomain);
		},
		onError: (error: ApiRequestFailed) => {
			props.failCallback(error.data.message);
		}
	})

	function onDomainSelected(domain: string) {
		setSelectedDomain(domain);
	}

	return (
		<Dialog
			open={props.open}
			maxWidth={"md"}
		>
			<DialogTitle>Add New Domain to Google Postmaster</DialogTitle>
			<DialogContent>
				<Stack direction={"column"} spacing={4} sx={{mt: 4}}>
					<Stack direction={"column"} spacing={2}>
						<Typography variant={"body1"}>
							1. Please select the domain you wish to connect with Google Postmaster.
						</Typography>
						<FormControl fullWidth>
							<InputLabel id="domain-select-label">Available Domains</InputLabel>
							<Select
								labelId="domain-select-label"
								value={selectedDomain}
								label="Available Domains"
								onChange={e => onDomainSelected(e.target.value)}
							>
								{props.availableDomains.map((domain, index) => {
									return (
										<MenuItem key={index} value={domain}>{domain}</MenuItem>
									);
								})}
							</Select>
						</FormControl>
					</Stack>
					{/* TXT Record Input */}
					<Stack direction={"column"} spacing={2} sx={{mt: 4}}>
						<Typography variant={"body1"}>
							2. Visit <a href="https://postmaster.google.com" target={"_blank"} rel={"noreferrer"}>
							https://postmaster.google.com</a> and
							add <b>{selectedDomain || "your domain"}</b>. Once done, copy the TXT value, paste it below and click on
							Confirm.
						</Typography>
						<textarea placeholder={"Enter TXT record value..."}
											value={txtValue}
											rows={5}
											disabled={!selectedDomain}
											onChange={e => setTxtValue(e.target.value)}/>
					</Stack>
				</Stack>
				{/* TXT Record Input */}
				<Stack direction={"column"} spacing={2} sx={{mt: 4}}>
					<Typography variant={"body1"}>
						3. After clicking on below Confirm button, go to Google Postmaster and click on "Verify".
					</Typography>
				</Stack>
			</DialogContent>
			<DialogActions>

				<Button onClick={() => {
					onDomainSelected("");
					props.close();
				}}>
					Cancel
				</Button>

				<Button disabled={!(txtValue && selectedDomain) || addDomainMutation.isPending}
								onClick={() => {
									addDomainMutation.mutate()
								}}>
					Confirm
				</Button>

			</DialogActions>
		</Dialog>
	)
}

function DeleteDomainButton(props: {
	domain: string,
	successCallback: (domain: string) => void,
	failCallback: (message: string) => void,
}) {
	const [open, setOpen] = React.useState(false);

	// Mutation to remove domain.
	const removeDomainMutation = useMutation({
		mutationKey: ["googlePostmasterRemoveDomainMutation"],
		mutationFn: () => authenticateAndPostData("/integrations/google-postmaster/remove/", {
			domain: props.domain,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			props.successCallback(props.domain);
			handleClose();
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			props.failCallback(error.data.message);
			handleClose();
		}
	})

	const handleClickOpen = () => {
		setOpen(true);
	};
	const handleClose = () => {
		setOpen(false);
	};

	return (
		<React.Fragment>
			<Button startIcon={<Delete/>} color={"error"} onClick={handleClickOpen}>
				Remove
			</Button>
			<Dialog
				open={open}
				onClose={handleClose}
				aria-labelledby="remove-domain-alert-dialog-title"
				aria-describedby="remove-domain-alert-dialog-description"
			>
				<DialogTitle id="remove-domain-alert-dialog-title">
					Confirm removing {props.domain}?
				</DialogTitle>
				<DialogContent>
					<DialogContentText id="alert-dialog-description">
						This will remove <b>{props.domain}</b> from your Google Postmaster integration on <b>Deliveryman.ai</b>.
						The domain will still be active on Postmaster until you remove it from there too. Proceed?
					</DialogContentText>
				</DialogContent>
				<DialogActions>
					<Button onClick={handleClose}>
						Cancel
					</Button>
					<Button disabled={removeDomainMutation.isPending}
									onClick={() => {
										removeDomainMutation.mutate();
									}}>
						Proceed
					</Button>
				</DialogActions>
			</Dialog>
		</React.Fragment>
	);
}
