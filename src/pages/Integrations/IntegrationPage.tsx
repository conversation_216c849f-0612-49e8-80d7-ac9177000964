import {<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@mui/material";
import {Delete, Add} from "@mui/icons-material";
import { ReactComponent as HubspotLogo } from "@assets/images/hubspot.svg";
import { ReactComponent as SalesforceLogo } from "@assets/images/salesforce.svg";
import { ReactComponent as SmartleadLogo } from "@assets/images/smartlead.svg";
import { ReactComponent as InstantlyLogo } from "@assets/images/instantly.svg";
import {useState, useEffect} from "react";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndPostData, authenticateAndFetchData, retryFn} from "@lib/apis";
import {useSnackbar} from "notistack";
import {useDialogs} from "@toolpad/core";


interface AuthUrl {
    auth_url: string
    is_integrated: boolean
}

export default function IntegrationPage(){
    const isProduction = process.env.REACT_APP_BACKEND === "https://api.deliveryman.ai";

    const {enqueueSnackbar} = useSnackbar();
    const dialogs = useDialogs();
    const integrations = [
        { name: "Hubspot", Logo: HubspotLogo },
        { name: "Salesforce", Logo: SalesforceLogo },
        { name: "Instantly", Logo: InstantlyLogo },
        { name: "Smartlead", Logo: SmartleadLogo },
    ];
    
    const [integrationRequest, setIntegrationRequest] = useState("")
    const [integrationExplanation, setIntegrationExplanation] = useState("")
    const [hubspotAuthUrl, setHubspotAuthUrl] = useState("")
    const [isConnected, setIsConnected] = useState(false);
    
    const authUrlQuery = useQuery({
        queryKey: ["getAuthUrl"],
        queryFn: () => authenticateAndFetchData(
            '/hubspot/get-auth-url/'
        ),
        refetchOnWindowFocus: false,
        gcTime: 0,
        retry: retryFn,
    });

    const sendIntegrationRequest = useMutation({
        mutationKey: ["sendIntegrationRequest"],
        mutationFn: () => authenticateAndPostData("/integration-request/", {
            integration_request: integrationRequest,
            integration_explaination: integrationExplanation,
        }),
        gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			enqueueSnackbar("Integration request has been sent", {
				variant: "success",
			});
            setIntegrationRequest("")            
            setIntegrationExplanation("")
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
    }) 

    const deleteHubspotIntegrationMutation = useMutation({
        mutationKey: ["deleteHubspotMutation"],
        mutationFn: () => authenticateAndPostData("/hubspot/delete-hubspot-integration/", {}),
        gcTime: 0,
        retry: retryFn,
        onSuccess: () => {
            enqueueSnackbar("Hubspot removed successfully.", {
                variant: "success",
            });   
            authUrlQuery.refetch().then()         
        },
        onError: (error: ApiRequestFailed) => {
            console.error(error);
            enqueueSnackbar(error.data.message, {
                variant: "error",
            });
        }
    })

    // Page Title
    useEffect(() => {
        document.title = "HubSpot Integration - Deliveryman.ai";
    }, []);
    
    useEffect(()=>{
        if(authUrlQuery.data){
            let data = authUrlQuery.data.data as AuthUrl;
            console.log("data",data)
            setHubspotAuthUrl(data.auth_url)
            setIsConnected(data.is_integrated)
        }
    },[authUrlQuery.data])

    useEffect(() => {
        const params = new URLSearchParams(window.location.search);
        if (params.get("ok") === "true") {
            setIsConnected(true);
        }
    }, []);

    const handleDelete = async () => {
        const confirmed = await dialogs.confirm(
            "Are you sure you want to remove Hubspot integration?",
            {
                title: "Remove Integration",
                cancelText: "Cancel",
                okText: "Remove",
            }
        );

        if (!confirmed) return;

        deleteHubspotIntegrationMutation.mutate();
    };


    const handleConnect = () => {
        if (isConnected) {
            // show delete confirmation instead
            handleDelete();
        } else {
            if (hubspotAuthUrl) {
                window.location.href = hubspotAuthUrl;
            }
        }
    };


    return (
        <Stack direction={"column"} spacing={4} sx={{pb: 4, height: "100%"}}>
            <Stack direction={"column"} spacing={1}>
                <Typography variant={"h4"} fontWeight={"bold"} align={"center"} color={"primary"}>
                    Integrations
                </Typography>
                <Typography variant={"body1"} align={"center"}>
                    Connect and manage your integrations in one place.<br />
                    Easily add new integrations or update and remove existing ones.
                </Typography>
            </Stack>
            <Grid container spacing={3} justifyContent="center" sx={{ px: 2 }}>
                {integrations.map((integration) => (
                <Grid item xs={12} sm={6} md={3} key={integration.name}>
                    <Card
                    sx={{
                        textAlign: "center",
                        p: 3,
                        borderRadius: 3,
                        boxShadow: 3,
                        height: "100%",
                    }}
                    >
                    <CardContent>
                        <Box
                        sx={{
                            height: 60,
                            mb: 2,
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                        }}
                        >
                        <integration.Logo
                            style={{
                            height: "100%",
                            maxHeight: "60px",
                            width: "auto",
                            }}
                        />
                        </Box>
                        <Typography variant="h6" fontWeight="bold">
                        {integration.name}
                        </Typography>
                        {integration.name === "Hubspot" && !isProduction ? (
                            <Button
                            variant="text"
                            size="small"
                            sx={{ mt: 1 }}
                            onClick={handleConnect}                            
                            startIcon={isConnected ? <Delete /> : <Add />}
                            >
                            {isConnected ? "Remove App" : "Connect App"}
                            </Button>
                        ) : (
                            <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ mt: 1 }}
                            >
                            Coming soon
                            </Typography>
                        )}
                    </CardContent>
                    </Card>
                </Grid>
                ))}
            </Grid>
            <Divider sx={{mt: 2, mb: 2}}/>
            <Stack spacing={3} sx={{ maxWidth: 500, mx: "auto", width: "100%" }}>      
                <Stack direction="column" spacing={2}>
                <Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
                        What integration do you need?			
                </Typography>
                <TextField
                    value={integrationRequest}
                    onChange={(e) => setIntegrationRequest(e.target.value)}
                    label="What integration do you need?"
                    variant="outlined"
                    fullWidth
                    required
                />
                </Stack>
                <Stack direction="column" spacing={2}>
                    <Typography variant={"subtitle1"} sx={{ fontWeight: "bold" }}>
                    Explain what you need about the integration
                    </Typography>
                    <TextField
                    value={integrationExplanation}
                    onChange={(e) => setIntegrationExplanation(e.target.value)}
                    label="Describe your requirement"
                    multiline
                    rows={4}
                    variant="outlined"
                    fullWidth
                    required
                    />
                </Stack>
                <Stack direction="row" justifyContent="flex-start">
                    <Button
                        variant="contained"
                        color="primary"
                        sx={{ whiteSpace: "nowrap" }}
                        onClick={() => {sendIntegrationRequest.mutate()}}
                    >
                        Send
                    </Button>
                </Stack>
            </Stack>
            <Divider sx={{mt: 2, mb: 2}}/>
        </Stack>
    )
}