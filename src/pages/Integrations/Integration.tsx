import {<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>A<PERSON>, <PERSON><PERSON>ontent, Container, Stack, Typography} from "@mui/material";
import {ChevronRight} from "@mui/icons-material";

import googleLogo from "@assets/images/google_g_logo.svg";
import {useEffect, useState} from "react";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {useSnackbar} from "notistack";
import {Link, useNavigate} from "react-router-dom";
import {urls} from "@routes";


interface PageData {
	status_code: string
	status_text: string

	google_auth_active: boolean
}


export default function Integration() {
	const {enqueueSnackbar} = useSnackbar();
	const navigate = useNavigate();

	const [pageData, setPageData] = useState<PageData>();

	// Fetch the page data
	const pageDataQuery = useQuery({
		queryKey: ["integrations"],
		queryFn: () => authenticateAndFetchData("/integrations/"),
		refetchOnWindowFocus: false,
		retry: retryFn,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data as PageData);
		}
	}, [pageDataQuery.data]);

	// Mutation to start google auth flow.
	const googleAuthMutation = useMutation({
		mutationKey: ["startGoogleAuthFlow"],
		mutationFn: () => authenticateAndPostData("/google-auth/start/", {}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: (response) => {
			console.log(response);
			let action = response.data["action"];
			switch (action) {
				case "redirect":
					// Send user to authenticate.
					let url = response.data["url"];
					window.location.href = url;
					break;

				default:
					// Not handling 'reauthenticated' action.
					console.error(`Unknown/unhandled action '${action}'`);
					break;
			}
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(
				error.data.message || "Something went wrong on our side. Please try again in some time.",
				{variant: "error"}
			);
		}
	});

	// Mutation to disconnect google services (postmaster) integration.
	const googleAuthRevoke = useMutation({
		mutationKey: ["googleAuthRevoke"],
		mutationFn: () => authenticateAndPostData("/google-auth/revoke/", {}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			enqueueSnackbar("Google Postmaster integration has been removed successfully.", {
				variant: "success",
			})
			navigate(0);
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error"
			});
		}
	});


	function startGoogleAuth() {
		googleAuthMutation.mutate();
	}

	function removeGoogleIntegration() {
		googleAuthRevoke.mutate();
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={4} justifyContent={"center"} alignItems={"center"}>
				<Container>
					<Typography variant="h3" align={"center"} color={"primary"}>Integrations</Typography>
					<Typography variant="subtitle1" align={"center"}>
						Seamlessly connect your favorite tools to supercharge your email campaigns.
					</Typography>
				</Container>

				<Box display={"flex"} flexDirection={"row"} justifyContent={"flex-start"} flexWrap={"wrap"}
						 sx={{width: "100%"}}>
					{/* ================== Google Postmaster Integration ================== */}
					<Card sx={{maxWidth: 345, padding: "1em"}}>
						<CardContent>
							<Box display={"flex"} flexDirection={"row"} justifyContent={"flex-start"} alignItems={"center"}>
								<img src={googleLogo} alt={"Google Logo"} style={{width: "32px", height: "32px"}}/>
								<Typography variant="h5" component="div" sx={{ml: 2}}>
									Google Postmaster
								</Typography>
							</Box>
							<Typography variant="body2" sx={{color: 'text.secondary', mt: 2}}>
								Integrate Google Postmaster with Deliveryman.ai to keep track of your email sending domain health and
								spam rate.
							</Typography>
						</CardContent>
						<CardActions>
							{!pageData.google_auth_active ?
								<Button variant={"contained"}
												size="small"
												color={"success"}
												disabled={googleAuthMutation.isPending}
												onClick={startGoogleAuth}>
									Connect
								</Button> :
								<Button variant={"contained"}
												size="small"
												color={"error"}
												disabled={googleAuthRevoke.isPending}
												onClick={removeGoogleIntegration}>
									Remove
								</Button>}

							<Button component={Link}
											to={urls["googlePostmaster"]}
											variant={"contained"}
											size="small"
											disabled={!pageData.google_auth_active}>
								View<ChevronRight/>
							</Button>
						</CardActions>
					</Card>
				</Box>
			</Stack>
		)

	} else {
		// Ideally it should not reach here.
		return <></>;
	}
}
