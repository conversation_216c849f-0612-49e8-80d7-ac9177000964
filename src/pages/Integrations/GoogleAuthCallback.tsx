import {useNavigate, useSearchParams} from "react-router-dom";
import {Box, Typography} from "@mui/material";
import {useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import {useEffect} from "react";
import {urls} from "@routes";
import {useSnackbar} from "notistack";

export default function GoogleAuthCallback() {
	const [searchParams] = useSearchParams();
	const navigate = useNavigate();
	const {enqueueSnackbar} = useSnackbar();

	// Run query for finishing auth flow.
	const authCallbackQuery = useQuery({
		queryKey: ["authCallbackQuery"],
		queryFn: () => authenticateAndFetchData(
			searchParams.get("error") === null ?
				`/google-auth/callback/?code=${searchParams.get("code")}&state=${searchParams.get("state")}` :
				`/google-auth/callback/?error=${searchParams.get("error")}&state=${searchParams.get("state")}`
			,
		),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (authCallbackQuery.data) {
			// Wait for 5 sec and then redirect user to integrations page.
			let responseData = authCallbackQuery.data.data;

			if (responseData["state"] === "success") {
				const redirectTimeout = setTimeout(() => {
					enqueueSnackbar("Google Postmaster integration has been completed successfully!", {
						variant: "success",
					});
					navigate(urls["integrations"]);
				}, 3000);
				return () => clearTimeout(redirectTimeout);

			} else {
				const redirectTimeout = setTimeout(() => {
					enqueueSnackbar("We were unable to complete google authentication due to some issue.", {
						variant: "error",
					});
					navigate(urls["integrations"]);
				}, 3000);
				return () => clearTimeout(redirectTimeout);
			}
		}
	}, [enqueueSnackbar, navigate, authCallbackQuery.data]);

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (authCallbackQuery.error as unknown as ApiRequestFailed) {
		let error = authCallbackQuery.error as unknown as ApiRequestFailed
		return (
			<Box display={"flex"} flexDirection={"column"}
					 justifyContent={"center"} alignItems={"center"}
					 sx={{width: "100%", mt: 12}}>
				<Typography variant={"h2"}>Oops! Something went wrong.</Typography>
				<Typography variant={"h5"} sx={{mt: 2}}>Error: {error.data.message}</Typography>
			</Box>
		)

	} else {
		return (
			<Box display={"flex"} flexDirection={"column"}
					 justifyContent={"center"} alignItems={"center"}
					 sx={{width: "100%", mt: 12}}>
				<Typography variant={"h2"}>Setting things up. Please wait...</Typography>
			</Box>
		)
	}
}
