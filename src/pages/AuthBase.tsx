import {Box, ThemeProvider} from "@mui/material";
import coldEmailerTheme from "../assets/themes/coldEmailerTheme";
import {Outlet} from "react-router-dom";
import {SnackbarProvider} from "notistack";

export default function AuthBase() {
	return (
		<ThemeProvider theme={coldEmailerTheme}>
			<SnackbarProvider>
				<Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" height="100vh">
					<Outlet/>
				</Box>
			</SnackbarProvider>
		</ThemeProvider>
	)
}
