import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	Dialog,
	DialogActions,
	DialogContent,
	DialogContentText,
	DialogTitle,
	Stack,
	TextField,
	Tooltip,
	Typography,
	Menu,
	MenuItem,
	useTheme
} from "@mui/material";
import {useEffect, useState} from "react";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {formatSecondsToReadableTime} from "@lib/utils";
import IconButton from "@mui/material/IconButton";
import {Add, Delete, Edit, PlayArrow, MoreVert} from "@mui/icons-material";
import {Link, useNavigate} from "react-router-dom";
import {urls} from "@routes";
import {useSnackbar} from "notistack";
import {ColDef, ValueFormatterParams} from "ag-grid-community";
import {CustomCellRendererProps} from "ag-grid-react";
import ServerSideDataTable from "@components/ServerSideDataTable";
import {useDialogs} from "@toolpad/core";
import { formatInTimeZone } from "date-fns-tz";

interface EmailAccount {
	id: number
	managed_subdomain: string

	postmaster_integration_active: boolean
	postmaster_http_error: boolean
	domain_reputation: "HIGH" | "MEDIUM" | "LOW" | "BAD" | null
	spam_ratio: number | null

	email_subdomain_count: number
	total_emails: number
	max_contacts: number
	setup_complete: boolean
	created_on_ts: number
	status: string
	seconds_remaining: number
	black_list_status: string[]
	redirect_domain: string | null
	email_sending_limit: number
}


export default function EmailAccounts() {
	const {enqueueSnackbar} = useSnackbar();
	const theme = useTheme();
	const dialogs = useDialogs();

	const [
		emailAccounts,
		setEmailAccounts
	] = useState<EmailAccount[]>([]);
	const [timezone, setTimezone] = useState("")

	// Page Title
	useEffect(() => {
    document.title = "Email Sending Domains - Deliveryman.ai";
  }, []);

	// Fetch the page data.
	const pageDataQuery = useQuery({
		queryKey: ["emailAccounts"],
		queryFn: () => authenticateAndFetchData("/email-accounts/"),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			setEmailAccounts(pageDataQuery.data.data["managed_subdomains"]);
			setTimezone(pageDataQuery.data.data["timezone"])
		}
	}, [pageDataQuery.data]);

	// Mutation to delete managed subdomain from account.
	const removeManagedSubdomainMutation = useMutation({
		mutationKey: ["removeManagedSubdomain"],
		mutationFn: (subdomain: string) => authenticateAndPostData("/connect/remove-managed-subdomain/", {
			subdomain: subdomain,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: (response) => {
			pageDataQuery.refetch().then();
			console.log(`${response.data["subdomain"]} disconnected from account`);
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	// const domainRepMapper = (value: "HIGH" | "MEDIUM" | "LOW" | "BAD" | null) => {
	// 	switch (value) {
	// 		case "HIGH":
	// 			return "GOOD";
	//
	// 		case "MEDIUM":
	// 			return "NEUTRAL";
	//
	// 		case "LOW":
	// 			return "BAD";
	//
	// 		case "BAD":
	// 			return "WORST";
	//
	// 		case null:
	// 			return "NEUTRAL";
	//
	// 		default:
	// 			return "n/a";
	// 	}
	// }

	// const spamRatioMapper = (value: number | null) => {
	// 	if (value === null) return "NIL";
	//
	// 	// Get percentage.
	// 	value = value * 100;
	//
	// 	if (value >= 0.3) {
	// 		return "HIGH"
	//
	// 	} else if (value >= 0.01) {
	// 		return "LOW"
	//
	// 	} else {
	// 		return "NIL"
	//
	// 	}
	// }

	// const PostmasterDomainReputationCell = (cellProps: CustomCellRendererProps) => {
	// 	if (cellProps.data["postmaster_integration_active"]) {
	// 		return <>{domainRepMapper(cellProps.value)}</>;
	//
	// 	} else if (cellProps.data["postmaster_http_error"]) {
	// 		return <>---</>;
	//
	// 	} else {
	// 		return <Button component={Link} to={urls["integrations"]} size={"small"}>Integrate</Button>
	// 	}
	// }

	// const PostmasterSpamRate = (cellProps: CustomCellRendererProps) => {
	// 	if (cellProps.data["postmaster_integration_active"]) {
	// 		return <>{spamRatioMapper(cellProps.value)}</>;
	//
	// 	} else if (cellProps.data["postmaster_http_error"]) {
	// 		return <>---</>;
	//
	// 	} else {
	// 		return <Button component={Link} to={urls["integrations"]} size={"small"}>Integrate</Button>
	// 	}
	// }
	function timestampFormatter(params: ValueFormatterParams, timezone: string) {
		if (!params.value) return "";

		return formatInTimeZone(
			new Date(params.value),
			timezone,
			"hh:mm a, do MMM yyyy"
		).replace(/\b(AM|PM)\b/, (match) => match.toLowerCase());
	}

	const EmailsLink = (params: CustomCellRendererProps) => {
		return (
			<Link style={{color: theme.palette.text.primary}} to={urls["emails"].replace(":domain", params.value)}>
				{params.value}
			</Link>
		)
	}

	const blackListStatus = (params: CustomCellRendererProps) => {
		const value = params.value;

		if (!value || value === "Healthy") {
			return (
				<Chip
					size="small"
					color="success"
					label="Healthy"
					sx={{ fontSize: "0.75rem", height: 24 }}
				/>
			);
		}

		if (value === "Incomplete") {
			return (
				<Chip
					size="small"
					color="default"
					label="Incomplete"
					sx={{ fontSize: "0.75rem", height: 24 }}
				/>
			);
		}

		if (value === "Unknown Error") {
			return (
				<Chip
					size="small"
					color="error"
					label="Unknown Error"
					sx={{ fontSize: "0.75rem", height: 24 }}
				/>
			);
		}

		const sources = typeof value === "string" ? value.split(",") : [];

		return (
			<Box sx={{ mt: 1 }}>
				<Stack spacing={0.5} sx={{ maxWidth: 180 }}>
					{sources.slice(0, 3).map((source: string, idx: number) => (
						<Tooltip title={source.trim()} key={idx}>
							<Chip
								size="small"
								color="warning"
								label={source.trim()}
								sx={{
									maxWidth: "100%",
									overflow: "hidden",
									textOverflow: "ellipsis",
									whiteSpace: "nowrap",
									fontSize: "0.75rem",
									height: 24,
								}}
							/>
						</Tooltip>
					))}
					{sources.length > 3 && (
						<Chip
							size="small"
							color="warning"
							label={`+${sources.length - 3} more`}
							sx={{ fontSize: "0.75rem", height: 24 }}
						/>
					)}
				</Stack>
			</Box>
		);
	};

	// const DeleteButton = (params: CustomCellRendererProps) => {
	// 	return (
	// 		<Tooltip title={"Delete this email account."}>
	// 			<IconButton color={"error"}
	// 									disabled={removeManagedSubdomainMutation.isPending}
	// 									onClick={async () => {
	// 										const deleteConfirmed = await dialogs.confirm(
	// 											`Are you sure you want to remove the domain ${params.data["managed_subdomain"]} from this account?`,
	// 											{
	// 												title: "Remove Domain",
	// 												okText: "Confirm",
	// 												cancelText: "Cancel",
	// 												severity: "error"
	// 											});
	// 										if (deleteConfirmed) {
	// 											removeManagedSubdomainMutation.mutate(params.data["managed_subdomain"]);
	// 										}
	// 									}}>
	// 				<Delete/>
	// 			</IconButton>
	// 		</Tooltip>
	// 	)
	// }

	// const ResumeSetupButton = (params: CustomCellRendererProps) => {
	// 	console.log(params.data["setup_complete"]);
	// 	if (!params.data["setup_complete"]) {
	// 		return (
	// 			<Tooltip title={"Continue email account setup"}>
	// 				<IconButton component={Link}
	// 										to={urls["connectNewDomain"] + `?subdomain=${params.data["managed_subdomain"]}`}>
	// 					<PlayArrow color={"success"}/>
	// 				</IconButton>
	// 			</Tooltip>
	// 		)

	// 	} else {
	// 		return null
	// 	}
	// }

	const DomainRedirectCell = (cellProps: CustomCellRendererProps) => {
		return (
			<>
				{cellProps.value || "---"}{"  "}<DomainRedirectionDialog managedSubdomain={cellProps.data["managed_subdomain"]}
																																 redirect_to={cellProps.data["redirect_domain"]}/>
			</>
		)
	}

	const AddNewDomainButton = () => {
		return (
			<Button component={Link}
							// to={urls["connectDomainOptions"]}
							to={urls["connectNewDomain"]}
							startIcon={<Add/>}
							variant="contained"
							color="primary">
				Add New Domain
			</Button>
		)
	}

	const StatusBadgeCell = (customProps: CustomCellRendererProps) => {
		if (customProps.data["setup_complete"]) {
			if (customProps.value === "active") {
				return <Chip size={"small"} color={"success"} label={"Active"}/>

			} else if (customProps.value === "warmup") {
				return <Chip size={"small"} color={"warning"}
										 label={formatSecondsToReadableTime(customProps.data["seconds_remaining"] as number)}/>

			} else if (customProps.value === "deleting") {
				return <Chip size={"small"} color={"error"} label={"Deleting..."}/>
			}

		} else {
			if (customProps.value === "deleting") {
				return <Chip size={"small"} color={"error"} label={"Deleting..."}/>
			} else {
				return <Chip size={"small"} color={"default"} label={"Incomplete"}/>
			}
		}
	}

	const ActionsButton = (props: CustomCellRendererProps) => {
		const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
		const open = Boolean(anchorEl);
		const navigate = useNavigate();

		const handleClick = (event: React.MouseEvent<HTMLElement>) => {
			setAnchorEl(event.currentTarget);
		};

		const handleClose = () => {
			setAnchorEl(null);
		};

		const handleResumeSetup = () => {
			const subdomain = props.data["managed_subdomain"];
			navigate(`${urls["connectNewDomain"]}?subdomain=${subdomain}`);
			handleClose();
		};

		const handleDelete = async () => {
			const subdomain = props.data["managed_subdomain"];
			const deleteConfirmed = await dialogs.confirm(
				`Are you sure you want to remove the domain ${subdomain} from this account?`,
				{
					title: "Remove Domain",
					okText: "Confirm",
					cancelText: "Cancel",
					severity: "error",
				}
			);
			if (deleteConfirmed) {
				removeManagedSubdomainMutation.mutate(subdomain);
			}
			handleClose();
		};

		return (
			<>
				<IconButton
					aria-label="more"
					aria-controls={open ? "long-menu" : undefined}
					aria-expanded={open ? "true" : undefined}
					aria-haspopup="true"
					onClick={handleClick}
				>
					<MoreVert />
				</IconButton>
				<Menu
					id="long-menu"
					MenuListProps={{
						"aria-labelledby": "long-button",
					}}
					anchorEl={anchorEl}
					open={open}
					onClose={handleClose}
				>
					{/* Resume Setup Option */}
					{!props.data["setup_complete"] && (
						<MenuItem onClick={handleResumeSetup}>
							<PlayArrow color="success" sx={{ mr: 1 }}/>
							<Typography variant="subtitle2">Resume Setup</Typography>
						</MenuItem>
					)}

					{/* Delete Option */}
					<MenuItem onClick={handleDelete}>
						<Delete color="error" sx={{ mr: 1 }}/>
						<Typography variant="subtitle2">Delete Domain</Typography>
					</MenuItem>
				</Menu>
			</>
		);
	};


	// Columns for email accounts table.
	const columnDefs: ColDef[] = [
		{field: "managed_subdomain", headerName: "Managed Subdomain", cellRenderer: EmailsLink, flex: 2},
		// {
		// 	field: "domain_reputation",
		// 	headerName: "Domain Reputation",
		// 	cellRenderer: PostmasterDomainReputationCell,
		// 	sortable: false
		// },
		// {field: "spam_ratio", headerName: "Spam Rate", cellRenderer: PostmasterSpamRate, sortable: false},
		{field: "created_on_ts", headerName: "Created On", valueFormatter: (params) => timestampFormatter(params, timezone), flex: 2},
		{field: "email_subdomain_count", headerName: "Email Subdomains"},
		{field: "total_emails", headerName: "Total Emails"},
		{field: "email_sending_limit", headerName: "Email Sending Limit"},		
		// {field: "max_contacts", headerName: "Max Contacts"},
		{
			field: "redirect_domain",
			headerName: "Domain Redirection",
			cellRenderer: DomainRedirectCell,
			sortable: false,
			flex: 2
		},
		{field: "status", headerName: "Status", cellRenderer: StatusBadgeCell, sortable: false},
		{field: "black_list_status", headerName: "Blacklist Status", cellRenderer: blackListStatus, sortable: false},
		// {headerName: "Resume Setup", cellRenderer: ResumeSetupButton, sortable: false},
		// {headerName: "Delete", cellRenderer: DeleteButton, resizable: false, maxWidth: 80, sortable: false},
		{headerName: "Actions", cellRenderer: ActionsButton, sortable: false, resizable: false},
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading || pageDataQuery.isRefetching) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else {
		return (
			<Stack direction={"column"} spacing={4} sx={{pb: 4, height: "100%"}}>
				<Stack direction={"column"} spacing={1}>
					<Typography variant={"h4"} fontWeight={"bold"} align={"center"} color={"primary"}>
						Email Sending Domains
					</Typography>
					<Typography variant={"body1"} align={"center"}>
						Connect your domains to DeliveryMan.ai to automatically generate multiple email inboxes for sending at
						scale.
					</Typography>
				</Stack>
				<Box sx={{height: "100%"}}>
					<ServerSideDataTable columns={columnDefs}
										rows={emailAccounts}
										actions={AddNewDomainButton()}
										getRowHeight={(params) => {
										const value = params?.data?.black_list_status;
										if (!value || value === "Healthy" || value === "Unknown Error") {
										return 42;
										}
										const sources = typeof value === "string" ? value.split(",") : [];
										const visibleChips = Math.min(sources.length, 4);
										return visibleChips * 32 + 8;
										}}/>
				</Box>
			</Stack>
		)
	}
}


function DomainRedirectionDialog(props: {
	managedSubdomain: string,
	redirect_to: string | null,
}) {
	const {enqueueSnackbar} = useSnackbar();
	const navigate = useNavigate();

	const [open, setOpen] = useState(false);
	const [domain, setDomain] = useState<string>(props.redirect_to || "");

	const handleClickOpen = () => {
		setOpen(true);
	};

	const handleClose = () => {
		setOpen(false);
	};

	const setupMutation = useMutation({
		mutationKey: ["setupMutation"],
		mutationFn: () => authenticateAndPostData("/domain-redirect/setup/", {
			"managed_domain": props.managedSubdomain,
			"target_domain": domain,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			navigate(0);
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	const deleteMutation = useMutation({
		mutationKey: ["deleteMutation"],
		mutationFn: () => authenticateAndPostData("/domain-redirect/delete/", {
			"managed_domain": props.managedSubdomain,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			navigate(0);
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<>
			<IconButton size={"small"} color={"primary"} onClick={handleClickOpen}>
				<Edit/>
			</IconButton>
			<Dialog
				open={open}
				onClose={handleClose}
			>
				<DialogTitle>Set Up Domain Redirection</DialogTitle>
				<DialogContent>
					<DialogContentText>
						By Setting up domain redirection, you can redirect visting web traffic for all email
						subdomains created under <b>{props.managedSubdomain}</b> to some other domain.
						<br/><br/>
						Where should we redirect them to?
					</DialogContentText>
					<TextField
						autoFocus
						required
						type="text"
						label="Enter target domain/subdomain (ex. example.com)"
						value={domain}
						onChange={e => setDomain(e.target.value)}
						variant="outlined"
						sx={{mt: 2}}
						fullWidth
					/>
					<Alert severity={"info"} sx={{mt: 2}}>
						It might take a few minutes for changes to be reflected due to DNS caching.
					</Alert>
					<Alert severity={"info"} sx={{mt: 2}}>
						Make sure the target site/domain is running on <b>HTTPS</b>.
					</Alert>
				</DialogContent>
				<DialogActions sx={{justifyContnt: "space-between"}}>
					{props.redirect_to &&
              <Button color={"error"}
                      disabled={setupMutation.isPending || deleteMutation.isPending}
                      onClick={() => {
												deleteMutation.mutate();
											}}>
                  Delete
              </Button>}
					<Button onClick={handleClose}
									disabled={setupMutation.isPending || deleteMutation.isPending}>
						Cancel
					</Button>
					<Button disabled={!domain || setupMutation.isPending || deleteMutation.isPending}
									onClick={() => {
										setupMutation.mutate();
									}}>
						Proceed
					</Button>
				</DialogActions>
			</Dialog>
		</>
	);
}

