import {
	Accordion,
	AccordionDetails,
	AccordionSummary,
	Box,
	Button,
	Container,
	FormControl,
	FormControlLabel,
	Paper,
	Radio,
	RadioGroup,
	Stack,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	TextField,
	Typography
} from "@mui/material";
import {ChevronLeft, Close, CreditCard, ExpandMore, NavigateNext, TaskAlt, WarningAmber} from "@mui/icons-material";
import {Link, useNavigate, useSearchParams} from "react-router-dom";
import {urls} from "@routes";
import {Dispatch, useEffect, useReducer, useState} from "react";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import {mirage} from "ldrs";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import CopyButton from "@components/CopyButton";
import {DataGrid, GridColDef} from "@mui/x-data-grid";
import {useSnackbar} from "notistack";
import {format} from "date-fns";
import CountrySelect from "@components/CountrySelect";

mirage.register();

interface PageData {
	status_code: number;
	status_text: string;

	setup_data?: {
		setup_complete: boolean
		current_stage: number
		subdomain: string
		nameservers: string[]
		naming_strategy: string
		custom_name: string | null
		contacts_count: number
	}
}

// ------------------ WIZARD REDUCER ------------------

type State = {
	setupComplete: boolean
	stageNumber: number
	subdomain: string
	nameservers: string[],
	namingStrategy: string,
	customName: string,
	contactsCount: number,
}

type LoadSavedStateAction = {
	type: "LOAD_SAVE_STATE",
	setupComplete: boolean,
	stageNumber: number,
	subdomain: string,
	nameservers: string[],
	namingStrategy: string,
	customName: string,
	contactsCount: number,
};
type AddManagedSubdomainAction = { type: "ADD_MANAGED_SUBDOMAIN", subdomain: string, nameservers: string[] };
type NameserversAddedAction = { type: "NAMESERVERS_ADDED" };
type NameserversVerifiedAction = { type: "NAMESERVERS_VERIFIED" };
type NamingStrategySetAction = {
	type: "NAMING_STRATEGY_SET",
	namingStrategy: string,
	customName: string,
	contactsCount: number
};
type EmailGenerationPollingAction = { type: "EMAIL_GENERATION_POLLING" };
type EmailSetupPollingAction = { type: "EMAIL_SETUP_POLLING" };
type EmailAliasesCreated = { type: "EMAIL_ALIASES_CREATED" }

type Action = LoadSavedStateAction | AddManagedSubdomainAction | NameserversAddedAction | NameserversVerifiedAction |
	NamingStrategySetAction | EmailAliasesCreated | EmailGenerationPollingAction | EmailSetupPollingAction;

function reducer(wizardData: State, action: Action): State {
	switch (action.type) {
		case 'LOAD_SAVE_STATE':
			return {
				setupComplete: action.setupComplete,
				stageNumber: action.stageNumber,
				subdomain: action.subdomain,
				nameservers: action.nameservers,
				namingStrategy: action.namingStrategy,
				customName: action.customName,
				contactsCount: action.contactsCount,
			}

		case 'ADD_MANAGED_SUBDOMAIN':
			return {
				...wizardData,
				stageNumber: 1,
				subdomain: action.subdomain,
				nameservers: action.nameservers
			}

		case 'NAMESERVERS_ADDED':
			return {...wizardData, stageNumber: 2};

		case 'NAMESERVERS_VERIFIED':
			return {...wizardData, stageNumber: 3};

		case 'NAMING_STRATEGY_SET':
			return {
				...wizardData,
				stageNumber: 4,
				namingStrategy: action.namingStrategy,
				contactsCount: action.contactsCount,
				customName: action.customName,
			};

		case 'EMAIL_GENERATION_POLLING':
			return {
				...wizardData,
				stageNumber: 5
			}

		case 'EMAIL_ALIASES_CREATED':
			return {
				...wizardData,
				stageNumber: 6,
			};

		case 'EMAIL_SETUP_POLLING':
			return {
				...wizardData,
				stageNumber: 7, // Setup complete page.
			};

		default:
			throw new Error("Invalid action type");
	}
}

// ------------------------------------------------------

export default function ConnectExistingDomain() {
	const initalState: State = {
		setupComplete: false,
		stageNumber: 0,
		subdomain: "",
		nameservers: [],
		namingStrategy: "",
		customName: "",
		contactsCount: 0,
	}

	const [searchParams] = useSearchParams();
	const continueSubdomain: string | null = searchParams.get("subdomain");

	const [wizardData, dispatch] = useReducer(reducer, initalState);

	const [showLoader, setShowLoader] = useState(false);

	// --------------- Fetch the page data ---------------
	const pageDataQuery = useQuery({
		queryKey: ["connectExistingDomain"],
		queryFn: () => authenticateAndFetchData(continueSubdomain ?
			`/connect-exising-domain/?subdomain=${continueSubdomain}` :
			"/connect-exising-domain/"
		),
		refetchOnWindowFocus: false,
		gcTime: 0,
		retry: retryFn,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			let data = pageDataQuery.data.data as PageData;
			if (data.setup_data) {
				dispatch({
					type: "LOAD_SAVE_STATE",
					setupComplete: data.setup_data.setup_complete,
					stageNumber: data.setup_data.current_stage,
					subdomain: data.setup_data.subdomain,
					nameservers: data.setup_data.nameservers,
					namingStrategy: data.setup_data.naming_strategy,
					customName: data.setup_data.custom_name || "",
					contactsCount: data.setup_data.contacts_count,
				});
			}
		}
	}, [pageDataQuery.data]);

	// --------------- WIZARD STAGES ---------------
	const stages = [
		<GetSubdomain dispatch={dispatch}
									setShowLoader={setShowLoader}/>,
		<AddNameservers nameservers={wizardData.nameservers}
										subdomain={wizardData.subdomain}
										dispatch={dispatch}/>,
		<CheckNsRecords subdomain={wizardData.subdomain}
										dispatch={dispatch}/>,
		<EmailAndSubdomainGeneration subdomain={wizardData.subdomain}
																 dispatch={dispatch}
																 setShowLoader={setShowLoader}/>,
		<EmailGenerationPollingPage subdomain={wizardData.subdomain} dispatch={dispatch}/>,
		<EmailList subdomain={wizardData.subdomain}
							 dispatch={dispatch}
							 setShowLoader={setShowLoader}/>,
		<EmailSetupPollingPage subdomain={wizardData.subdomain} dispatch={dispatch}/>,
		<SetupComplete subdomain={wizardData.subdomain}/>,
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	function renderPage() {
		if (showLoader) {
			return <WizardProcessing/>;
		} else {
			return stages[wizardData.stageNumber];
		}
	}

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		if (pageDataQuery.error?.statusCode === 403) {
			return (
				<Box display={"flex"} flexDirection={"column"} justifyContent={"center"} alignItems={"center"}>
					<Typography variant={"h4"} textAlign={"center"}>Max Domain Connection Limit Reached</Typography>
					<Typography variant={"body1"} textAlign={"center"} sx={{mt: 2}}>
						You have reached your current plan's maximum allowed domain connections.<br/>
						Please upgrade to a higher plan for higher limits or delete any of your existing domains to make space.
					</Typography>
					<Stack direction={"row"} sx={{mt: 4}} spacing={4}>
						<Button variant={"contained"} component={Link} to={urls["dashboard"]} startIcon={<ChevronLeft/>}>
							Dashboard
						</Button>
						<Button variant={"contained"} component={Link} to={urls["manageSubscription"]} startIcon={<CreditCard/>}>
							Manage Subscription
						</Button>
					</Stack>
				</Box>
			)
		} else {
			return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>
		}

	} else {
		return (
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"}
					 flexDirection={"column"}>
				{renderPage()}
			</Box>
		)
	}
}

function WizardProcessing() {
	return (
		<Stack direction={"column"} spacing={4} justifyContent={"center"} alignItems={"center"}>
			<Stack direction={"column"} spacing={1}>
				<Typography variant={"h4"} align={"center"}>Processing... Please Wait.</Typography>
				<Typography variant={"subtitle1"} align={"center"}>
					Getting things ready for the next step. Please do not close this tab or refresh the page!
				</Typography>
			</Stack>
			<l-mirage size="120" color={"#42a5f5"}></l-mirage>
		</Stack>
	)
}

function CancelButton(props: {
	disable?: boolean
}) {
	const navigate = useNavigate();

	return (
		<Button variant={"outlined"} sx={{mr: 1}} startIcon={<Close/>} disabled={props.disable}
						onClick={() => {
							navigate(urls["emailAccounts"]);
						}}>
			Cancel
		</Button>
	)
}

function GetSubdomain(props: {
	dispatch: Dispatch<Action>,
	setShowLoader: (value: boolean) => void,
}) {
	const {enqueueSnackbar} = useSnackbar();

	const [domain, setDomain] = useState<string>("");
	// const [oldDomain, setOldDomain] = useState<boolean>(false);

	// --------------- Mutation for adding managed subdomain ---------------
	const addManagedSubdomainMutation = useMutation({
		mutationKey: ["addManagedSubdomain"],
		mutationFn: (fqdn: string) => authenticateAndPostData("/connect/add-managed-subdomain/", {
			fqdn: fqdn,
			next_stage_number: 1,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: (response) => {
			// Move to next stage
			props.setShowLoader(false);
			let nameservers: string[] = response.data["nameservers"];
			let subdomain: string = response.data["subdomain"];
			props.dispatch({
				type: "ADD_MANAGED_SUBDOMAIN",
				subdomain: subdomain,
				nameservers: nameservers,
			})
		},
		onError: (error: ApiRequestFailed) => {
			props.setShowLoader(false);
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	return (
		<>
			{/* Heading */}
			<Container maxWidth="sm">
				<Typography variant="h4" align={"center"} color={"primary"}>Add your domain</Typography>
				<Typography variant="subtitle1" align={"center"} sx={{ lineHeight: 1.5 }}>
					We will create sub-domains using this domain.<br/>
					The created sub-domain will then be used for creating email accounts.
				</Typography>
				{/* Alert Message */}
				<Accordion sx={{mt: 4, '&.MuiAccordion-root.Mui-expanded': {
						mt: 4
					}}}>
					<AccordionSummary
						expandIcon={<ExpandMore/>}
					>
						<WarningAmber sx={{mr: 1}} color={"warning"}/>
						<Typography component="span" fontWeight={"bold"}>
							Caution: Do not use your primary domain for sending cold emails.
						</Typography>
					</AccordionSummary>
					<AccordionDetails>
						If you already have a G-suite or a website on the domain you're connecting, 
						updating the NS records will stop them from functioning.
						It is advised that you buy/use a domain that doesn't have a mailbox or live website.
					</AccordionDetails>
				</Accordion>
			</Container>

			{/* Input */}
			<TextField id="domain"
								 value={domain}
								 onChange={e => setDomain(e.target.value)}
								 variant="outlined"
								 placeholder={"example.com"}
								 sx={{
									 mt: 4,
									 width: {
										 lg: "350px"
									 }
								 }}/>

			{/*/!* Domain 14 days old *!/*/}
			{/*<FormGroup sx={{mt: 4}}>*/}
			{/*	<FormControlLabel label="Domain was registered more than 14 days ago."*/}
			{/*										control={*/}
			{/*											<Checkbox value={oldDomain}*/}
			{/*																onChange={e => setOldDomain(e.target.checked)}/>*/}
			{/*										}/>*/}
			{/*</FormGroup>*/}

			{/* Actions */}
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"} flexDirection={"row"} sx={{mt: 4}}>
				<CancelButton/>
				<Button variant={"contained"} sx={{ml: 1}} endIcon={<NavigateNext/>}
								onClick={() => {
									props.setShowLoader(true);
									addManagedSubdomainMutation.mutate(domain);
								}}>
					Proceed
				</Button>
			</Box>
		</>
	)
}

function AddNameservers(props: {
	nameservers: string[],
	subdomain: string,
	dispatch: Dispatch<Action>,
}) {
	return (
		<>
			{/* Heading */}
			<Box sx={{maxWidth: 750}}>
				<Typography variant="h4" align={"center"} color={"primary"}>
					Update NS Record for <b>{props.subdomain}</b> & we'll auto-create your email sending infrastructure.
				</Typography>				

				{/* Table */}
				<Box sx={{mt: 4}}>
					<TableContainer component={Paper} sx={{maxWidth: "100%", overflowX: "auto"}}>
						<Table aria-label="simple table">
							<TableHead>
								<TableRow>
									<TableCell>Type</TableCell>
									<TableCell align="right">Name</TableCell>
									<TableCell align="right">Nameserver</TableCell>
								</TableRow>
							</TableHead>
							<TableBody>
								{props.nameservers.map((ns, index) => (
									<TableRow
										key={index}
										sx={{'&:last-child td, &:last-child th': {border: 0}}}
									>
										<TableCell component="th" scope="row">NS</TableCell>
										<TableCell align="right">{props.subdomain}&nbsp;<CopyButton text={props.subdomain}/></TableCell>
										<TableCell align="right">{ns}&nbsp;<CopyButton text={ns}/></TableCell>
									</TableRow>
								))}
							</TableBody>
						</Table>
					</TableContainer>
					<Stack justifyContent={"center"} alignItems={"center"} sx={{mt: 4}}>
						<Button variant={"contained"} onClick={() => {
							navigator.clipboard.writeText(props.nameservers.join("\n")).then(() => {
							});
						}}>
							Copy All Nameservers
						</Button>
					</Stack>
				</Box>

				{/* Additional Text */}
				<Box sx={{mt: 4}}>
					<Typography variant={"body1"} align={"center"}>
						How to add NS records on:&nbsp;
						<a href="https://deliveryman.ai/how-to-add-ns-records-in-namecheap" target={"_blank"}
							 rel={"noreferrer"}>Namecheap</a> |&nbsp;
						<a href="https://deliveryman.ai/how-to-add-ns-records-in-godaddy" target={"_blank"}
							 rel={"noreferrer"}>Godaddy</a> |&nbsp;
						<a href="https://deliveryman.ai/how-to-add-ns-records-in-porkbun" target={"_blank"}
							 rel={"noreferrer"}>Porkbun</a> |&nbsp;
						<a href="https://deliveryman.ai/how-to-add-ns-records-in-hostinger" target={"_blank"}
							 rel={"noreferrer"}>Hostinger</a>
					</Typography>
					<Typography variant={"body1"} align={"center"} sx={{mt: 4}}>
						If you are having trouble, please get in touch with us on
						our <span style={{textDecoration: "underline"}}>Live Chat Support</span>
					</Typography>
				</Box>

				{/* Actions */}
				<Box display={"flex"} justifyContent={"center"} alignItems={"center"} flexDirection={"row"} sx={{mt: 4}}>
					<CancelButton/>
					<Button variant={"contained"} sx={{ml: 1}} endIcon={<NavigateNext/>}
									onClick={() => {
										props.dispatch({
											type: 'NAMESERVERS_ADDED'
										});
									}}>
						I have added the records
					</Button>
				</Box>
			</Box>
		</>
	)
}

function CheckNsRecords(props: {
	subdomain: string,
	dispatch: Dispatch<Action>
}) {
	// Query for checking NS records
	const checkNsRecordsQuery = useQuery({
		queryKey: ["checkNsRecords"],
		queryFn: () => authenticateAndFetchData(`/connect/check-ns-records/?subdomain=${props.subdomain}`),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
		refetchInterval: 10000,
	});
	useEffect(() => {
		if (checkNsRecordsQuery.data) {
			console.log(checkNsRecordsQuery.data.data["success"])
			if (checkNsRecordsQuery.data.data["success"]) {
				props.dispatch({
					type: 'NAMESERVERS_VERIFIED'
				});
			}
		}
	}, [checkNsRecordsQuery.data, props]);

	return (
		<Stack direction={"column"} spacing={4} justifyContent={"center"} alignItems={"center"}>
			<Container maxWidth={"md"}>
				<Typography variant={"h4"} align={"center"} color={"primary"}>
					Verifying nameserver records for <b>{props.subdomain}</b>...
				</Typography>
				<Typography variant={"subtitle1"} align={"center"} sx={{mt: 2}}>
					It might take some time for the nameserver changes to propagate. Ideally this should only take
					between 5-10 mins or - in very rare cases - upto an hour.
					You can safely cancel and retry later if required.<br/><br/>
					In case you are stuck on this step, please
					get in touch with us on
					{/* eslint-disable-next-line no-script-url,jsx-a11y/anchor-is-valid */}
					our <a href={"javascript:void(Tawk_API.toggle())"} style={{whiteSpace: "nowrap"}}>Live Support Chat</a> for
					assistance.
				</Typography>
			</Container>
			<l-mirage size="120" color={"#42a5f5"}></l-mirage>
			{/* Actions */}
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"} flexDirection={"row"} sx={{mt: 4}}>
				<CancelButton/>
			</Box>
		</Stack>
	)
}

type NameStrategies = "random" | "male" | "female" | "custom"

function EmailAndSubdomainGeneration(props: {
	subdomain: string;
	dispatch: Dispatch<Action>,
	setShowLoader: (value: boolean) => void,
}) {
	const {enqueueSnackbar} = useSnackbar();

	const [nameStrategy, setNameStrategy] = useState<NameStrategies>("random");
	const [customFirstName, setCustomFirstName] = useState<string>("");
	const [customLastName, setCustomLastName] = useState<string>("");
	const [countryName, setCountryName] = useState<string>("");

	// Mutation for saving data.
	const saveDataMutation = useMutation({
		mutationKey: ["saveStrategyAndContactCount"],
		mutationFn: () => authenticateAndPostData("/connect/save-strategy-contact-count/", {
			subdomain: props.subdomain,
			naming_strategy: nameStrategy,
			custom_name: nameStrategy === "custom" ? `${customFirstName} ${customLastName}` : null,
			contacts_count: 2000,
			next_stage_number: 4,
			country: countryName,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			props.setShowLoader(false);
			// Move onto polling page.
			props.dispatch({
				type: "NAMING_STRATEGY_SET",
				namingStrategy: nameStrategy,
				customName: `${customFirstName} ${customLastName}`,
				contactsCount: 2000,
			});
		},
		onError: (error: ApiRequestFailed) => {
			props.setShowLoader(false);
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	})

	return (
		<Stack direction={"column"} spacing={4} justifyContent={"center"} alignItems={"center"}>
			{/* Heading */}
			<Container maxWidth="sm">
				<Typography variant="h4" align={"center"} color={"primary"}>
					Select Name for Email Sending Accounts for {props.subdomain}
				</Typography>
				<Typography variant="subtitle1" align={"center"}>
					Choose how to generate sender names for your email accounts. We will automatically create various email
					combinations based on your selection.
				</Typography>
			</Container>

			<Box display={"flex"} flexDirection={"column"} justifyContent={"center"} alignItems={"center"} sx={{mt: 4}}>
				{/* Name type selection */}
				<FormControl>
					<RadioGroup
						value={nameStrategy}
						onChange={e => setNameStrategy(e.target.value as NameStrategies)}
					>
						<FormControlLabel value="random" control={<Radio/>} label="Use Random Names"/>
						<FormControlLabel value="female" control={<Radio/>} label="Use Any Female Name"/>
						<FormControlLabel value="male" control={<Radio/>} label="Use Any Male Name"/>
						<FormControlLabel value="custom" control={<Radio/>} label="Use Custom Name"/>
					</RadioGroup>
				</FormControl>
				{/* Custom Name Input	*/}
				{nameStrategy === "custom" && <Stack direction={"column"} spacing={2} sx={{mt: 2}}>
            <TextField type={"text"}
                       label={"Enter first name"}
                       value={customFirstName}
                       onChange={e => setCustomFirstName(e.target.value)}
                       sx={{width: 400}}
                       disabled={nameStrategy !== "custom"} required/>
            <TextField type={"text"}
                       label={"Enter last name"}
                       value={customLastName}
                       onChange={e => setCustomLastName(e.target.value)}
                       sx={{width: 400}}
                       disabled={nameStrategy !== "custom"} required/>
        </Stack>}
				{/* Country Selection */}
				{nameStrategy !== "custom" && <Stack direction={"column"} spacing={1} sx={{mt: 2}}>
            <Typography fontWeight={"bold"}>Select A Country:</Typography>
            <CountrySelect onCountrySelected={setCountryName}/>
        </Stack>}
			</Box>

			{/*/!* Contacts count *!/*/}
			{/*<Box display={"flex"} flexDirection={"column"} justifyContent={"center"} alignItems={"center"} sx={{mt: 6}}>*/}
			{/*	<Typography variant={"h6"} align={"center"}>*/}
			{/*		How many contacts do you plan on sending email to?*/}
			{/*	</Typography>*/}
			{/*	<TextField type={"number"}*/}
			{/*						 label={"Enter value"}*/}
			{/*						 value={contactsCount}*/}
			{/*						 onChange={e => {*/}
			{/*							 try {*/}
			{/*								 setContactsCount(parseInt(e.target.value));*/}
			{/*							 } catch (e) {*/}
			{/*							 }*/}
			{/*						 }}*/}
			{/*						 sx={{mt: 2, width: 400}}/>*/}
			{/*	/!*	1 subdomain per 100. 10 emails per subdomain *!/*/}
			{/*	{contactsCount > 0 && <Typography variant={"body1"} align={"center"} sx={{mt: 4}}>*/}
			{/*      We'll generate <b>{Math.ceil(contactsCount / 100) * 10}</b> email accounts for you.*/}
			{/*  </Typography>}*/}
			{/*</Box>*/}

			{/* Actions */}
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"} flexDirection={"row"} sx={{mt: 4}}>
				<CancelButton disable={saveDataMutation.isPending}/>
				<Button variant={"contained"} sx={{ml: 1}} endIcon={<NavigateNext/>}
								disabled={saveDataMutation.isPending}
								onClick={() => {
									if (!nameStrategy) {
										enqueueSnackbar("Please set valid values for naming strategy.", {
											variant: "error",
										});
										return;
									}

									if (nameStrategy === "custom" && !(customLastName && customFirstName)) {
										enqueueSnackbar("Please provide valid firstname and lastname for custom naming strategy.", {
											variant: "error",
										});
										return;
									}

									if (nameStrategy !== "custom" && !countryName) {
										enqueueSnackbar("Please select a country.", {
											variant: "error",
										});
										return;
									}

									props.setShowLoader(true);
									saveDataMutation.mutate();
								}}>
					Proceed
				</Button>
			</Box>
		</Stack>
	)
}

function EmailGenerationPollingPage(props: {
	subdomain: string,
	dispatch: Dispatch<Action>
}) {
	// This will poll the given URL every 10 seconds. The URL should return "success" true or false. Polling will run
	// as long as "success" is false.
	const pollingQuery = useQuery({
		queryKey: ["emailGenerationPolling"],
		queryFn: () => authenticateAndFetchData(`/connect/check-email-generation-done/?subdomain=${props.subdomain}`),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
		refetchInterval: 10000,
	});
	useEffect(() => {
		if (pollingQuery.data) {
			if (pollingQuery.data.data["success"]) {
				props.dispatch({
					type: "EMAIL_GENERATION_POLLING",
				});
			}
		}
	}, [pollingQuery.data, props]);

	return (
		<Stack direction={"column"} spacing={4} justifyContent={"center"} alignItems={"center"}>
			<Container maxWidth={"md"}>
				<Typography variant={"h4"} align={"center"} color={"primary"}>
					Waiting for email id previews to be generated...
				</Typography>
				<Typography variant={"subtitle1"} align={"center"} sx={{mt: 2}}>
					This might take some time. In case you are stuck on this step, please
					get in touch with us on
					{/* eslint-disable-next-line no-script-url,jsx-a11y/anchor-is-valid */}
					our <a href={"javascript:void(Tawk_API.toggle())"} style={{whiteSpace: "nowrap"}}>Live Support Chat</a> for
					assistance.
				</Typography>
			</Container>
			<l-mirage size="120" color={"#42a5f5"}></l-mirage>
			{/*/!* Actions *!/*/}
			{/*<Box display={"flex"} justifyContent={"center"} alignItems={"center"} flexDirection={"row"} sx={{mt: 4}}>*/}
			{/*	<CancelButton/>*/}
			{/*</Box>*/}
		</Stack>
	)
}

interface EmailPreviewData {
	id: number
	email: string
	subdomain: string
}

function EmailList(props: {
	subdomain: string,
	dispatch: Dispatch<Action>,
	setShowLoader: (value: boolean) => void,
}) {
	const {enqueueSnackbar} = useSnackbar();
	const [data, setData] = useState<EmailPreviewData[]>([]);

	// Fetch the email data.
	const fetchPreviewQuery = useQuery({
		queryKey: ["fetchPreviewEmails"],
		queryFn: () => authenticateAndFetchData(`/connect/get-email-preview/?subdomain=${props.subdomain}`),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (fetchPreviewQuery.data) {
			let data = fetchPreviewQuery.data.data["preview"] as EmailPreviewData[];
			setData(data);
		}
	}, [fetchPreviewQuery.data]);

	// Mutation to set up email aliases.
	const setupEmailAliasesMutation = useMutation({
		mutationKey: ["setupEmailAliases"],
		mutationFn: () => authenticateAndPostData("/connect/setup-email-aliases/", {
			subdomain: props.subdomain,
			next_stage_number: 5,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			props.setShowLoader(false);
			props.dispatch({
				type: "EMAIL_ALIASES_CREATED",
			})
		},
		onError: (error: ApiRequestFailed) => {
			props.setShowLoader(false);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
			console.error(error.data.message);
		}
	});

	const columns: GridColDef[] = [
		{
			field: 'email',
			headerName: 'Email ID',
			flex: 1,
		},
		{
			field: 'subdomain',
			headerName: 'Subdomain',
			flex: 1,
		},
		// {
		// 	field: 'edit',
		// 	type: 'actions',
		// 	headerName: 'Edit Email',
		// 	width: 100,
		// 	getActions: ({id}) => {
		// 		return [
		// 			<GridActionsCellItem
		// 				icon={<Edit/>}
		// 				label="Edit"
		// 				onClick={() => handleEditClick(id)}
		// 			/>
		// 		]
		// 	}
		// },
	];

	// function handleEditClick(id: GridRowId) {
	// }

	const paginationModel = {page: 0, pageSize: 10};

	return (
		<Box display={"flex"} flexDirection={"column"} justifyContent={"center"} alignItems={"center"}
				 sx={{width: "100%", pb: 6}}>
			{/* Heading */}
			<Stack direction={"column"}>
				<Typography variant="h4" align={"center"} color={"primary"}>
					Email IDs That Will Be Generated.
				</Typography>
				<Typography variant="subtitle1" align={"center"}>
					We will be using these emails to send out emails to your contacts.<br/>
					We have generated {data.length} emails for you. There will be no charge for any of this.
				</Typography>
			</Stack>

			<Box sx={{width: {sm: "90%"}, mt: 4}}>
				<DataGrid
					rows={data}
					columns={columns}
					editMode="row"
					pageSizeOptions={[10, 50, 100]}
					initialState={{pagination: {paginationModel}}}
				/>
			</Box>

			{/* Actions */}
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"} flexDirection={"row"} sx={{mt: 4}}>
				<CancelButton disable={setupEmailAliasesMutation.isPending}/>
				<Button variant={"contained"}
								sx={{ml: 1}}
								endIcon={<NavigateNext/>}
								disabled={setupEmailAliasesMutation.isPending}
								onClick={() => {
									props.setShowLoader(true);
									setupEmailAliasesMutation.mutate();
								}}>
					Finish
				</Button>
			</Box>
		</Box>
	)
}

function EmailSetupPollingPage(props: {
	subdomain: string,
	dispatch: Dispatch<Action>
}) {
	// This will poll the given URL every 10 seconds. The URL should return "success" true or false. Polling will run
	// as long as "success" is false.
	const pollingQuery = useQuery({
		queryKey: ["emailSetupPolling"],
		queryFn: () => authenticateAndFetchData(`/connect/check-email-setup-done/?subdomain=${props.subdomain}`),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
		refetchInterval: 10000,
	});
	useEffect(() => {
		if (pollingQuery.data) {
			console.log(pollingQuery.data.data["success"]);
			if (pollingQuery.data.data["success"]) {
				props.dispatch({
					type: "EMAIL_SETUP_POLLING",
				});
			}
		}
	}, [pollingQuery.data, props]);

	return (
		<Stack direction={"column"} spacing={4} justifyContent={"center"} alignItems={"center"}>
			<Container maxWidth={"md"}>
				<Typography variant={"h4"} align={"center"} color={"primary"}>
					Waiting for Email ID setup to complete...
				</Typography>
				<Typography variant={"subtitle1"} align={"center"} sx={{mt: 2}}>
					This might take some time. In case you are stuck on this step, please
					get in touch with us on
					{/* eslint-disable-next-line no-script-url,jsx-a11y/anchor-is-valid */}
					our <a href={"javascript:void(Tawk_API.toggle())"} style={{whiteSpace: "nowrap"}}>Live Support Chat</a> for
					assistance.
				</Typography>
			</Container>
			<l-mirage size="120" color={"#42a5f5"}></l-mirage>
			{/*/!* Actions *!/*/}
			{/*<Box display={"flex"} justifyContent={"center"} alignItems={"center"} flexDirection={"row"} sx={{mt: 4}}>*/}
			{/*	<CancelButton/>*/}
			{/*</Box>*/}
		</Stack>
	)
}

interface SetupCompleteData {
	status_code: string
	status_text: string

	domain: string
	total_emails: number
	domain_usable_from_ts: number
}

function SetupComplete(props: {
	subdomain: string,
}) {
	const [
		setupCompleteData,
		setSetupCompleteData
	] = useState<SetupCompleteData>();

	const setupCompleteQuery = useQuery({
		queryKey: ["setupCompleteQuery", props.subdomain],
		queryFn: () => authenticateAndFetchData(`/connect/success-stage-data/?domain=${props.subdomain}`),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (setupCompleteQuery.data) {
			console.log(setupCompleteQuery.data.data);
			setSetupCompleteData(setupCompleteQuery.data.data);
		}
	}, [setupCompleteQuery.data]);

	if (setupCompleteQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (setupCompleteQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={setupCompleteQuery.error as unknown as ApiRequestFailed}/>

	} else if (setupCompleteData) {
		return (
			<Stack direction={"column"} spacing={4} justifyContent={"center"} alignItems={"center"}>
				{/* Heading */}
				<Paper elevation={2}
							 sx={{
								 p: 6,
								 maxWidth: "650px"
							 }}>
					<Stack direction={"column"} spacing={4}>
						<Typography variant={"h4"} fontWeight={"bold"}>
							Domain Successfully Connected!
						</Typography>
						<Stack direction={"column"} spacing={4}>

							<Stack direction={"row"} spacing={2}>
								<TaskAlt sx={{width: "35px", height: "35px"}} color={"success"}/>
								<Stack direction={"column"}>
									<Typography variant={"body1"} fontWeight={"bold"}>
										Domain Connection Successful
									</Typography>
									<Typography variant={"body2"}>
										Your domain <b>{setupCompleteData.domain}</b> is now properly connected to our servers.
									</Typography>
								</Stack>
							</Stack>

							<Stack direction={"row"} spacing={2}>
								<TaskAlt sx={{width: "35px", height: "35px"}} color={"success"}/>
								<Stack direction={"column"}>
									<Typography variant={"body1"} fontWeight={"bold"}>
										Subdomain Creation Successful
									</Typography>
									<Typography variant={"body2"}>
										All email subdomains have been created and configured.
									</Typography>
								</Stack>
							</Stack>

							<Stack direction={"row"} spacing={2}>
								<TaskAlt sx={{width: "35px", height: "35px"}} color={"success"}/>
								<Stack direction={"column"}>
									<Typography variant={"body1"} fontWeight={"bold"}>
										Email Address Creation Successful
									</Typography>
									<Typography variant={"body2"}>
										We have created <b>{setupCompleteData.total_emails}</b> custom email addresses for you.
									</Typography>
								</Stack>
							</Stack>
						</Stack>

						<Typography>
							We have started building your domain reputation.
							Your domain will be available for sending email campaigns
							from <b>{format(new Date(setupCompleteData.domain_usable_from_ts), "do MMM y, HH:mm:ss (xxx)")}</b> onwards.
						</Typography>

						<Button component={Link}
										to={urls["createCampaign"]}
										variant={"contained"}
										endIcon={<NavigateNext/>}>
							Create Campaign
						</Button>
					</Stack>
				</Paper>
			</Stack>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
