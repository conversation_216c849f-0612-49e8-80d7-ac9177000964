import {useEffect, useState} from "react";
import {useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {ColDef} from "ag-grid-community";
import {Box, Divider, Stack, Typography} from "@mui/material";
import ServerSideDataTable from "@components/ServerSideDataTable";
import {useNavigate, useParams} from "react-router-dom";
import IconButton from "@mui/material/IconButton";
import {ArrowBack} from "@mui/icons-material";
import {urls} from "@routes";

interface PageData {
	status_code: string
	status_text: string

	emails: EmailID[]
}

interface EmailID {
	address: string
	name: string
	subdomain: string
}


export default function Emails() {
	const {domain} = useParams();
	const navigate = useNavigate();

	const [pageData, setPageData] = useState<PageData>();

	// Query to fetch all users.
	const fetchAllEmails = useQuery({
		queryKey: ["fetchAllEmails"],
		queryFn: () => authenticateAndFetchData(`/email-accounts/emails/?domain=${domain || ""}`),
		gcTime: 0,
		refetchOnWindowFocus: false,
		retry: retryFn,
	});
	useEffect(() => {
		if (fetchAllEmails.data) {
			setPageData(fetchAllEmails.data.data as PageData);
		}
	}, [fetchAllEmails.data]);

	// Columns for user table.
	const columnDefs: ColDef[] = [
		{field: "address", headerName: "Email ID"},
		{field: "name", headerName: "Sender Name"},
		{field: "subdomain", headerName: "Email Subdomain"},
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (fetchAllEmails.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (fetchAllEmails.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={fetchAllEmails.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={4} sx={{pb: 4, height: "100%"}}>
				<Stack direction={"row"} spacing={2} alignItems={"center"}>
					<IconButton onClick={() => {
						navigate(urls["emailAccounts"])
					}}>
						<ArrowBack/>
					</IconButton>
					<Typography fontWeight={"bold"} variant={"h5"}>All Emails Under {domain || ""}</Typography>
				</Stack>

				<Divider/>
				<Box sx={{height: "100%"}}>
					<ServerSideDataTable columns={columnDefs} rows={pageData.emails}/>
				</Box>
			</Stack>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
