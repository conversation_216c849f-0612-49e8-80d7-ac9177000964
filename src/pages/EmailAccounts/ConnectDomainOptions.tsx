import {
	<PERSON>,
	<PERSON><PERSON>,
	Card,
	CardContent,
	List,
	ListItem,
	ListItemIcon,
	ListItemText,
	Stack,
	Typography
} from "@mui/material";
import {CheckBox} from "@mui/icons-material";
import {Link} from "react-router-dom";
import {urls} from "@routes";

export default function ConnectDomainOptions() {
	return (
		<Box>
			<Stack direction={"column"} spacing={2}>
				<Typography variant={"h4"} align={"center"}>
					Add Domain to Mass Create Email Sending Accounts
				</Typography>
			</Stack>
			<Stack direction={"row"} spacing={4} justifyContent={"center"} alignItems={"stretch"} sx={{mt: 4}}>
				{/* ------------------- Buy New Domain ------------------- */}
				<Card variant="elevation" sx={{maxWidth: "300px", p: 2}}>
					<CardContent>
						<Typography variant={"h5"} align={"center"} sx={{fontWeight: 700}}>
							Buy new domain & Use it to create email accounts.
						</Typography>
						<List sx={{mt: 2}}>
							<ListItem disablePadding>
								<ListItemIcon>
									<CheckBox color={"success"}/>
								</ListItemIcon>
								<ListItemText primary="Auto create subdomains."/>
							</ListItem>
							<ListItem disablePadding>
								<ListItemIcon>
									<CheckBox color={"success"}/>
								</ListItemIcon>
								<ListItemText primary="Auto manage DNS/NS records."/>
							</ListItem>
							<ListItem disablePadding>
								<ListItemIcon>
									<CheckBox color={"success"}/>
								</ListItemIcon>
								<ListItemText primary="Auto manage SPF/DKIM/DMARC."/>
							</ListItem>
							<ListItem disablePadding>
								<ListItemIcon>
									<CheckBox color={"success"}/>
								</ListItemIcon>
								<ListItemText primary="Auto create Email IDs."/>
							</ListItem>
						</List>
						<Button variant={"contained"} sx={{mt: 4}} disabled fullWidth>Coming Soon!</Button>
					</CardContent>
				</Card>
				{/* ------------------- Use Existing Domain ------------------- */}
				<Card variant="elevation" sx={{maxWidth: "300px", p: 2}}>
					<CardContent>
						<Typography variant={"h5"} align={"center"} sx={{fontWeight: 700}}>
							Use your existing domain & create email accounts.
						</Typography>
						<List sx={{mt: 2}}>
							<ListItem disablePadding>
								<ListItemIcon>
									<CheckBox color={"success"}/>
								</ListItemIcon>
								<ListItemText primary="Auto create subdomains."/>
							</ListItem>
							<ListItem disablePadding>
								<ListItemIcon>
									<CheckBox color={"success"}/>
								</ListItemIcon>
								<ListItemText primary="Auto manage DNS/NS records."/>
							</ListItem>
							<ListItem disablePadding>
								<ListItemIcon>
									<CheckBox color={"success"}/>
								</ListItemIcon>
								<ListItemText primary="Auto manage SPF/DKIM/DMARC."/>
							</ListItem>
							<ListItem disablePadding>
								<ListItemIcon>
									<CheckBox color={"success"}/>
								</ListItemIcon>
								<ListItemText primary="Auto create Email IDs."/>
							</ListItem>
						</List>
						<Button component={Link} to={urls["connectNewDomain"]} variant={"contained"} sx={{mt: 4}} fullWidth>
							Proceed
						</Button>
					</CardContent>
				</Card>
			</Stack>
		</Box>
	)
}
