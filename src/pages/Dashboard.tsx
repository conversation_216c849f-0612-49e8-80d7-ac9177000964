import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import {Avatar, Box, Card, CardContent, Grid2, Paper, Stack, Typography, useTheme, Container} from "@mui/material";
import {useEffect, useState} from "react";
import {useQuery} from "@tanstack/react-query";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";

interface PageData {
	status_code: string
	status_text: string

	total_campaigns: number
	total_contacts: number
	total_managed_subdomains: number
	total_emails: number
	total_emails_sent: number
	total_replies_received: number
	monthly_emails_remaining: number
}

export default function Dashboard() {
	const theme = useTheme();

	const [pageData, setPageData] = useState<PageData>();

	// Page Title
	useEffect(() => {
    document.title = "Dashboard - Deliveryman.ai";
  }, []);

	// Fetch the page data
	const pageDataQuery = useQuery({
		queryKey: ["dashboard"],
		queryFn: () => authenticateAndFetchData("/dashboard/"),
		refetchOnWindowFocus: false,
		retry: retryFn,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data as PageData);
		}
	}, [pageDataQuery.data]);

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Container sx={{pb: 6}}>
				<Stack direction={"column"} spacing={6}>
					{/* Stat Cards */}
					<Stack direction={"row"} spacing={2} flexWrap={"wrap"} useFlexGap={true}>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Campaigns</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_campaigns}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Leads</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_contacts}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Sending Domains</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_managed_subdomains}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Sending Emails</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_emails}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Emails Sent</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_emails_sent}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Replies Received</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_replies_received}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Email Credits Remaining</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.monthly_emails_remaining}</Typography>
								</Stack>
							</CardContent>
						</Card>
					</Stack>

					<Grid2 size={12}>
						<Card>
							<CardContent>
								<Typography variant={"h5"} fontWeight={"bold"}>
									Get Started in 3 Easy Steps
								</Typography>
								<Grid2 container spacing={3} sx={{mt: 2}}>
									{/* Step 1 */}
									<Grid2 size={{xs: 12, sm: 12, md: 4}} sx={{height: "100%"}}>
										<Paper sx={{
											px: 2,
											py: 3,
											height: "200px",
											bgcolor: theme.palette.mode === "light" ? "#F5F7FA" : "#1E293B",
										}} elevation={0}>
											<Stack direction={"column"} spacing={1} justifyContent={"center"} alignItems={"center"}>
												<Avatar sx={{bgcolor: 'primary.main', width: 40, height: 40, fontWeight: "bold"}}>
													1
												</Avatar>
												<Typography variant={"h5"} fontWeight={"bold"} align={"center"}>
													Connect Your Domain
												</Typography>
												<Typography variant={"body1"} align={"center"}>
													Set up your sending domain for better deliverability
												</Typography>
											</Stack>
										</Paper>
									</Grid2>
									{/* Step 2 */}
									<Grid2 size={{xs: 12, sm: 12, md: 4}} sx={{height: "100%"}}>
										<Paper sx={{
											px: 2,
											py: 3,
											height: "200px",
											bgcolor: theme.palette.mode === "light" ? "#F5F7FA" : "#1E293B",
										}} elevation={0}>
											<Stack direction={"column"} spacing={1} justifyContent={"center"} alignItems={"center"}>
												<Avatar sx={{bgcolor: 'primary.main', width: 40, height: 40, fontWeight: "bold"}}>
													2
												</Avatar>
												<Typography variant={"h5"} fontWeight={"bold"} align={"center"}>
													Import Your Leads
												</Typography>
												<Typography variant={"body1"} align={"center"}>
													Upload your contact list and start building campaigns
												</Typography>
											</Stack>
										</Paper>
									</Grid2>
									{/* Step 3 */}
									<Grid2 size={{xs: 12, sm: 12, md: 4}} sx={{height: "100%"}}>
										<Paper sx={{
											px: 2,
											py: 3,
											height: "200px",
											bgcolor: theme.palette.mode === "light" ? "#F5F7FA" : "#1E293B",
										}} elevation={0}>
											<Stack direction={"column"} spacing={1} justifyContent={"center"} alignItems={"center"}>
												<Avatar sx={{bgcolor: 'primary.main', width: 40, height: 40, fontWeight: "bold"}}>
													3
												</Avatar>
												<Typography variant={"h5"} fontWeight={"bold"} align={"center"}>
													Launch Campaign
												</Typography>
												<Typography variant={"body1"} align={"center"}>
													Create and send your first email sequence
												</Typography>
											</Stack>
										</Paper>
									</Grid2>
								</Grid2>
							</CardContent>
						</Card>
					</Grid2>

					{/* Youtube Video Player */}
					<Stack direction={"column"} justifyContent={"center"}>
						<Typography variant={"h4"} fontWeight={"bold"} align={"center"}>
							Get Started With Deliveryman.ai
						</Typography>
						<Box sx={{
							display: "flex",
							flexDirection: "row",
							justifyContent: "center",
							alignItems: "center",
							mt: 2
						}}>
							<iframe width="560" height="315" src="https://www.youtube.com/embed/Zt7hjRC0AMY?si=8kDjqo0yvGAkuHhV"
											title="YouTube video player" frameBorder="0"
											allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
											referrerPolicy="strict-origin-when-cross-origin" allowFullScreen></iframe>
						</Box>
					</Stack>
				</Stack>
			</Container>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
