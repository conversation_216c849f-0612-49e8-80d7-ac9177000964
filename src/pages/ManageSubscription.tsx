import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Switch, Typography} from "@mui/material";
import {useEffect, useState} from "react";
import PricingPlanCard from "@components/PricingPlanCard";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import StatCard from "@components/StatCard";
import {CreditCard, Email, EventAvailable, History, Launch} from "@mui/icons-material";
import {format} from "date-fns";

interface PageData {
	status_code: string
	status_text: string

	plans: SubscriptionPlan[]
	current_plan: string
	next_limit_renewal_ts: number
	total_email_limit: number
	remaining_email_limit: number
	domain_limit: number
	total_domains_connected: number
	stripe_portal_available: boolean
	current_billing_period: "monthly" | "annual" | null
	paid_user: boolean
}

interface SubscriptionPlan {
	id: number
	name: string
	monthly_amount: number
	annual_amount: number
	popular: boolean
	monthly_feature_list: string[]
	annual_feature_list: string[]
	current_plan: boolean
}

export default function ManageSubscription() {
	const [pageData, setPageData] = useState<PageData>();
	const [annual, setAnnual] = useState(false);

	// Query to fetch page data.
	const pageDataQuery = useQuery({
		queryKey: ["manageSubscription"],
		queryFn: () => authenticateAndFetchData("/manage-subscription/"),
		refetchOnWindowFocus: false,
		retry: retryFn,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data as PageData);
		}
	}, [pageDataQuery.data]);

	// Mutation for opening stripe customer portal.
	const stripeCustomerPortalMutation = useMutation({
		mutationKey: ["stripeCustomerPortalMutation"],
		mutationFn: () => authenticateAndPostData("/get-stripe-portal-url/", {}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: (response) => {
			window.location.href = response.data["url"];
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
		}
	});

	function stripePortalHandler() {
		stripeCustomerPortalMutation.mutate();
	}

	function checkIfCurrentPlan(currentPlan: boolean, currentBillingPeriod: "monthly" | "annual" | null) {
		if (currentBillingPeriod === null) {
			return false;

		} else {
			return currentPlan && ((annual && currentBillingPeriod === "annual") || (!annual && currentBillingPeriod === "monthly"));
		}
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={4} sx={{pb: 4}}>
				<Grid2 container spacing={3}>
					<Grid2 size={{xs: 12, sm: 6, md: 4}}>
						<StatCard
							icon={<CreditCard/>}
							heading="Current Subscription Plan"
							value={`${pageData.current_plan} (${pageData.current_billing_period})`}
							color="#009688"
						/>
					</Grid2>
					<Grid2 size={{xs: 12, sm: 6, md: 4}}>
						<StatCard
							icon={<Email/>}
							heading="Credits Remaining"
							value={`${pageData.remaining_email_limit}/${pageData.total_email_limit}`}
							color="#3f51b5"
						/>
					</Grid2>
					<Grid2 size={{xs: 12, sm: 6, md: 4}}>
						<StatCard
							icon={<Email/>}
							heading="Domain Limit"
							value={`${pageData.total_domains_connected}/${pageData.domain_limit}`}
							color="#3f51b5"
						/>
					</Grid2>
					<Grid2 size={{xs: 12, sm: 6, md: 4}}>
						<StatCard
							icon={<EventAvailable/>}
							heading="Next Renewal Date"
							value={format(new Date(pageData.next_limit_renewal_ts), "do MMM y, HH:mm:ss (xxx)")}
							color="#ff5722"
						/>
					</Grid2>
					<Grid2 size={{xs: 12, sm: 6, md: 4}}>
						<StatCard
							icon={<History/>}
							heading="View Invoice & Cancel Plan"
							value={
								<Button startIcon={<Launch/>}
												disabled={!pageData.stripe_portal_available || stripeCustomerPortalMutation.isPending}
												onClick={stripePortalHandler}>
									Stripe Portal
								</Button>
							}
							color="#90caf9"
						/>
					</Grid2>
				</Grid2>
				{/* Heading */}
				<Stack direction={"column"} spacing={1}>
					<Typography variant={"h4"} fontWeight={"bold"} align={"center"} color={"primary"}>
						Manage Your Workspace Subscription
					</Typography>
					<Typography variant={"body1"} align={"center"}>
						Upgrade, downgrade, or modify your plan anytime. Keep track of your usage and billing history for
						this workspace.
					</Typography>
				</Stack>
				{/* Plan Cards */}
				<Stack direction={"column"} alignItems={"center"} justifyContent={"center"} spacing={4} sx={{width: "100%"}}>
					<Stack direction={"row"} spacing={1} sx={{alignItems: 'center'}}>
						<Typography>Monthly</Typography>
						<Switch
							checked={annual}
							onChange={e => {
								setAnnual(e.target.checked);
							}}
						/>
						<Typography>Annual</Typography>
					</Stack>
					<Grid2 container spacing={3} justifyContent={"center"} sx={{width: "100%"}}>
						{pageData.plans.map(plan => (
							<Grid2 size={{xs: 12, sm: 12, md: 3}} key={plan.id}>
								<PricingPlanCard planId={plan.id}
																 planName={plan.name}
																 annualPrice={plan.annual_amount}
																 monthlyPrice={plan.monthly_amount}
																 isMonthly={!annual}
																 monthlyFeatureList={plan.monthly_feature_list}
																 annualFeatureList={plan.annual_feature_list}
																 isPopular={plan.popular}
																 currentPlan={checkIfCurrentPlan(plan.current_plan, pageData.current_billing_period)}
																 paidUser={pageData.paid_user}/>
							</Grid2>
						))}
					</Grid2>
				</Stack>
			</Stack>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
