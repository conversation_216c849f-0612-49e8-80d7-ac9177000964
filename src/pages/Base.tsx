import DashboardIcon from '@mui/icons-material/Dashboard';
import {AppProvider} from '@toolpad/core/react-router-dom';
import {DashboardLayout} from '@toolpad/core/DashboardLayout';
import {Navigation} from "@toolpad/core";
import {Link, Outlet, useNavigate} from "react-router-dom";
import coldEmailerTheme from "../assets/themes/coldEmailerTheme";
import {
	AddCircle,
	AlternateEmail,
	Campaign, Checklist,
	ContactMail,
	CreditCard,
	DeviceHub,
	Info,
	LiveHelp,
	Logout,
	Send,
	Settings,
	Update,
} from "@mui/icons-material";
import {
	Box,
	Button,
	Card,
	CardActions,
	CardContent,
	Dialog,
	DialogActions,
	DialogContent,
	DialogTitle,
	FormControl,
	InputLabel,
	MenuItem,
	Select,
	SelectChangeEvent,
	Stack,
	TextField,
	Typography,
	useTheme
} from "@mui/material";
import {SnackbarProvider, useSnackbar} from "notistack";
import white_logo from "@assets/branding/coldemailer_white_full_logo.svg";
import black_logo from "@assets/branding/coldemailer_black_full_logo.svg";

import TawkMessengerReact from '@tawk.to/tawk-messenger-react';
import CalendyButton from "@components/CalendyButton";
import {useEffect, useState} from "react";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import {urls} from "@routes";


const NAVIGATION: Navigation = [
	{
		segment: 'dashboard',
		title: 'Dashboard',
		icon: <DashboardIcon/>,
	},
	{
		segment: 'campaigns',
		title: 'Campaigns',
		icon: <Campaign/>,
	},
	{
		segment: 'email-accounts',
		title: 'Email Sending Domains',
		icon: <AlternateEmail/>,
	},
	{
		segment: 'contact-lists',
		title: 'Contact Lists',
		icon: <ContactMail/>,
	},
	// {
	// 	segment: 'integrations',
	// 	title: 'Integrations',
	// 	icon: <DeviceHub/>,
	// },
	{
		segment: 'sending-history',
		title: 'Sending History',
		icon: <Send/>,
	},
	{
		segment: 'integrations-page',
		title: (
		<span>
			Integrations{" "}
			<span style={{ fontSize: "0.75em" }}>
			(coming soon)
			</span>
		</span>
		) as unknown as string,
		icon: <DeviceHub />,
	},
	{
		segment: 'settings',
		title: 'Settings',
		icon: <Settings/>,
	},
	{
		segment: 'faq',
		title: 'FAQ',
		icon: <Info/>,
	},
	{
		segment: 'manage-subscription',
		title: 'Manage Subscription',
		icon: <CreditCard/>,
	},
	{
		segment: 'updates',
		title: 'Updates',
		icon: <Update/>,
	},
	{
		action: (
			<CalendyButton/>
		)
	},
	{
		kind: "divider"
	},
	{
		segment: 'logout',
		title: 'Logout',
		icon: <Logout/>,
	},
];


interface PageData {
	status_code: string
	status_text: string

	monthly_emails_remaining: number
	free_plan_user: number
	user_verified: boolean
}

declare global {
	interface Window {
		Tawk_API?: any;
		Tawk_LoadStart?: Date;
	}
}

export default function Base() {

	useEffect(() => {
		window.Tawk_API = window.Tawk_API || {};
		window.Tawk_LoadStart = new Date();

		window.Tawk_API.onLoad = function () {
			window.Tawk_API.hideWidget();
			window.Tawk_API.setAttributes(
				{'visibility': 'hidden'},
				function (error) {
					if (error) console.error('Tawk visibility error:', error);
				}
			);
		};

	}, []);


	const [pageData, setPageData] = useState<PageData>();

	// Query to fetch Base page data.
	const pageDataQuery = useQuery({
		queryKey: ["loggedInBasePageData"],
		queryFn: () => authenticateAndFetchData("/logged-in-user-base-page/"),
		refetchOnWindowFocus: false,
		retry: retryFn,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data as PageData);
		}
	}, [pageDataQuery.data]);


	// // Add elements that need to appear at top, next to dark/light mode toggle.
	// const ToolbarActions = () => {
	// 	if (pageData) {
	// 		return (
	// 			<Stack direction={"row"} alignItems={"center"} justifyContent={"center"} spacing={1}>
	// 				<Chip label={`Credits: ${pageData.monthly_emails_remaining}`}/>
	// 			</Stack>
	// 		);
	//
	// 	} else {
	// 		return <></>
	// 	}
	// };

	return (
		<AppProvider
			navigation={NAVIGATION}
			theme={coldEmailerTheme}
			branding={{
				logo: <BrandingLogo/>,
				title: "",
			}}
		>
			<SnackbarProvider>
				<DashboardLayout slots={{
					"sidebarFooter": () => {
						return (
							<SidebarFooter/>
						)
					},
					// toolbarActions: ToolbarActions
				}}>
					<TawkMessengerReact
						propertyId="677bd660af5bfec1dbe75235"
						widgetId="1igtrqqqt"/>
					<Box sx={{
						p: 3,
						height: "100%"
					}}>
						{pageData && !pageData.user_verified && (
							<Box
								sx={{
									backgroundColor: "#ffeeeb",
									border: "1px solid #ffe08a",
									borderRadius: 1,
									p: 2,
									mb: 2,
									display: "flex",
									alignItems: "center",
									justifySelf: "center",
									width: '70%',
								}}
							>
								<Typography sx={{color: "#000"}}>

									📬 <b>Verify your email</b> to start sending cold emails. Didn't get verification email? Resend
									from {" "}
									<Link to="/settings" style={{color: "#000", textDecoration: "underline"}}>
										Settings page
									</Link>.
								</Typography>
							</Box>
						)}
						{pageData?.free_plan_user && <FreePlanAlert/>}
						<Outlet/>
					</Box>
				</DashboardLayout>
			</SnackbarProvider>
		</AppProvider>
	);
}


function FreePlanAlert() {
	return (
		<Card variant={"outlined"} sx={{mb: 4}}>
			<CardContent>
				<Typography variant={"body2"}>
					⚠️ IMPORTANT: The free plan has now been discontinued 🙁<br/>
					<br/>
					We have been sustaining hundreds of users with thousands of domains that costs $$$ since the time we
					launched.
					With the growing userbase and costs to run domain warmups, we are forced to discontinue the free
					plans.
					I request you to upgrade to a paid plan to continue using DeliverymanAI.
				</Typography>
			</CardContent>
			<CardActions>
				<Button component={Link} to={urls["manageSubscription"]} startIcon={<CreditCard/>}>
					Purchase Subscription
				</Button>
			</CardActions>
		</Card>
	)
}

export function BrandingLogo() {
	const isDarkTheme = useTheme().palette.mode === 'dark'

	if (isDarkTheme) {
		return (
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"} sx={{height: "100%"}}>
				<img src={white_logo} alt={"logo"} style={{width: "10em", height: "auto"}}/>
			</Box>
		)
	} else {
		return (
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"} sx={{height: "100%"}}>
				<img src={black_logo} alt={"logo"} style={{width: "10em", height: "auto"}}/>
			</Box>
		)
	}
}

function SidebarFooter() {
	const [isChatVisible, setIsChatVisible] = useState(false);

	const handleLiveChatClick = () => {
		if (window.Tawk_API) {
			if (isChatVisible) {
				window.Tawk_API.minimize();
				setIsChatVisible(false);
			} else {
				window.Tawk_API.maximize();
				setIsChatVisible(true);
			}
		}
	};

	return (
		<Stack direction={"column"} sx={{width: "100%", p: 3}} spacing={3}>
			<Button
				href="https://deliveryman.ai/todo"
				target="_blank"
				rel="noopener noreferrer"
				variant="contained"
				startIcon={<Checklist/>}
			>
				Our Todo List
			</Button>
			<Button
				onClick={handleLiveChatClick}
				variant={"contained"}
				color={"info"}
				startIcon={<LiveHelp/>}>
				Live Chat Help
			</Button>
			<WorkspaceSelection/>
		</Stack>
	)
}

interface Workspace {
	id: number
	name: string
}

function WorkspaceSelection() {
	const theme = useTheme();
	const navigate = useNavigate();

	const [
		selectedWorkspaceId,
		setSelectedWorkspaceId
	] = useState<string>("");
	const [
		workspaces,
		setWorkspaces
	] = useState<Workspace[]>([]);
	const [
		openModal,
		setOpenModal
	] = useState<boolean>(false);

	// Query to fetch workspaces.
	const fetchWorkspacesQuery = useQuery({
		queryKey: ["fetchWorkspaces"],
		queryFn: () => authenticateAndFetchData("/workspaces/"),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	})
	useEffect(() => {
		if (fetchWorkspacesQuery.data) {
			setWorkspaces(fetchWorkspacesQuery.data.data["workspaces"] as Workspace[]);
			setSelectedWorkspaceId((fetchWorkspacesQuery.data.data["active_workspace"] as Workspace).id.toString())
		}
	}, [fetchWorkspacesQuery.data]);

	// Mutation to switch to new workspace.
	const switchWorkspaceMutation = useMutation({
		mutationKey: ["switchWorkspace"],
		mutationFn: (workspaceID: string) => authenticateAndPostData("/workspaces/switch/", {
			"workspace_id": workspaceID
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			refreshPage();
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
		}
	});

	const handleChange = (event: SelectChangeEvent) => {
		// setSelectedWorkspaceId(event.target.value as string);
		switchWorkspaceMutation.mutate(event.target.value as string);
	};

	const handleCloseModal = () => {
		setOpenModal(false);
	}

	const refreshPage = () => {
		navigate(0);
	}

	return (
		<FormControl sx={{width: "100%"}}>
			<InputLabel id="workspace-selection-label">Select Workspace</InputLabel>
			<Select
				labelId={"workspace-selection-label"}
				id="workspace-selection"
				value={selectedWorkspaceId}
				label={"Select Workspace"}
				onChange={handleChange}
				inputProps={{'aria-label': 'Without label'}}
				sx={{width: "100%"}}
				MenuProps={{
					anchorOrigin: {
						vertical: 'top',
						horizontal: 'left',
					},
					transformOrigin: {
						vertical: 'bottom', // Transform the dropdown to appear above the input
						horizontal: 'left',
					},
					PaperProps: {
						style: {
							marginTop: -10, // Adjust this value to move the dropdown upwards
							border: `1px solid ${theme.palette.grey[700]}`,
							paddingTop: 4,
							paddingBottom: 4
						},
					},
					elevation: 0,
				}}
			>
				{workspaces.map((workspace) => (
					<MenuItem value={workspace.id.toString()} key={workspace.id}>{workspace.name}</MenuItem>
				))}
				<Box sx={{p: 1, pb: 0, mt: 2}}>
					<Button variant={"outlined"}
									sx={{width: "100%"}}
									startIcon={<AddCircle/>}
									onClick={() => {
										setOpenModal(true);
									}}>
						New Workspace
					</Button>
				</Box>
			</Select>
			<CreateNewWorkspaceModal open={openModal} onClose={handleCloseModal} refreshPage={refreshPage}/>
		</FormControl>
	)
}

function CreateNewWorkspaceModal(props: {
	open: boolean,
	onClose: () => void,
	refreshPage: () => void,
}) {
	const {enqueueSnackbar} = useSnackbar();
	const [newWorkspaceName, setNewWorkspaceName] = useState("");

	const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
	// Mutation to create and switch to new workspace.
	const createWorkspaceMutation = useMutation({
		mutationKey: ["createWorkspace"],
		mutationFn: (name: string) => authenticateAndPostData("/workspaces/create/", {
			"workspace_name": name,
			"timezone": timezone
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			props.refreshPage();
			handleClose();
		},
		onError: (error: ApiRequestFailed) => {
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
			console.error(error);
		}
	});

	const handleConfirm = () => {
		if (newWorkspaceName && newWorkspaceName.length < 300) {
			createWorkspaceMutation.mutate(newWorkspaceName);

		} else {
			console.error("Please enter a valid workspace name less than 300 characters.")
		}
	};

	const handleClose = () => {
		setNewWorkspaceName("");
		props.onClose();
	}

	return (
		<Dialog open={props.open}
						onClose={handleClose}>
			<DialogTitle>Create New Workspace</DialogTitle>
			<DialogContent>
				<Typography variant={"body2"}>
					Provide a fitting name for your new workspace.
				</Typography>
				<TextField
					label="Name"
					type="text"
					fullWidth
					value={newWorkspaceName}
					sx={{mt: 2}}
					onChange={(e) => setNewWorkspaceName(e.target.value)}
				/>
			</DialogContent>
			<DialogActions>
				<Button onClick={handleClose}>Cancel</Button>
				<Button onClick={handleConfirm} color="primary" disabled={createWorkspaceMutation.isPending}>
					Create
				</Button>
			</DialogActions>
		</Dialog>
	);
}
