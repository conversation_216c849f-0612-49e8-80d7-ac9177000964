import {<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typo<PERSON>, useTheme} from "@mui/material";
import {Link, useNavigate} from "react-router-dom";
import {urls} from "@routes";
import {useState} from "react";
import {useMutation} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndPostData} from "@lib/apis";
import {saveToken} from "@lib/jwt";
import {useSnackbar} from "notistack";
import white_logo from "@assets/branding/coldemailer_white_full_logo.svg";
import black_logo from "@assets/branding/coldemailer_black_full_logo.svg";

interface LoginSuccessData {
	status_code: number
	status_text: string
	access_token: string
	refresh_token: string
}

function BrandingLogo() {
	const isDarkTheme = useTheme().palette.mode === 'dark'

	if (isDarkTheme) {
		return (
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"}>
				<img src={white_logo} alt={"logo"} style={{width: "18em", height: "auto"}}/>
			</Box>
		)
	} else {
		return (
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"}>
				<img src={black_logo} alt={"logo"} style={{width: "18em", height: "auto"}}/>
			</Box>
		)
	}
}

export default function Login() {
	const navigate = useNavigate();
	const {enqueueSnackbar} = useSnackbar();

	const [email, setEmail] = useState<string>("");
	const [password, setPassword] = useState<string>("");

	const loginMutation = useMutation({
		mutationKey: ['login', email, password],
		mutationFn: () => authenticateAndPostData(
			"/auth/login/",
			{"email": email, "password": password}
		),
		gcTime: 0,
		onSuccess: (response) => {
			// Set tokens & send user to dashboard page.
			let data: LoginSuccessData = response.data;
			if (data.access_token && data.refresh_token) {
				saveToken("access", data.access_token);
				saveToken("refresh", data.refresh_token);
				navigate(urls["dashboard"]);
			} else {
				// Ideally should not happen. But just in case.
				console.error("Oops, Something went wrong on our end! Please try again.")
			}
		},
		onError: (error: ApiRequestFailed) => {
			// Show error message.
			console.error(error.data.message)
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	function handleLogin() {
		if (email && password) {
			loginMutation.mutate();
		} else {
			console.error("Missing email / password");
			enqueueSnackbar("Missing email / password", {
				variant: "error",
			});
		}
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<Container maxWidth={"xs"}>
			<BrandingLogo/>
			<Stack direction="column" spacing={1} sx={{mt: 6}}>
				<Typography variant="h3" align={"center"}>Log In</Typography>
				<Typography variant="h5" align={"center"}>Welcome! Please log in to continue.</Typography>
			</Stack>
			<form
				onSubmit={(e) => {
					e.preventDefault();
					handleLogin();
				}}
			>
			<Stack direction="column" spacing={2} marginTop={5}>
				<TextField type="email"
									 id="email"
									 label="Email ID"
									 variant="outlined"
									 value={email}
									 onChange={e => setEmail(e.target.value)}
									 required/>
				<TextField type="password"
									 id="password"
									 label="Password"
									 variant="outlined"
									 value={password}
									 onChange={e => setPassword(e.target.value)}
									 required/>
			</Stack>
			<Stack direction="column" spacing={2} marginTop={5}>
				<Button variant="contained" color="primary" type="submit"
								disabled={loginMutation.isPending}>
					Sign In
				</Button>
				<Stack direction={"column"} spacing={2}>
					<Typography variant="body2" align={"center"}>
						Don't have an account? <Link to={urls["signup"]}>Sign Up</Link>.
					</Typography>
					<Typography variant="body2" align={"center"}>
						Forgot Password? <Link to={urls["resetPassword"]}>Reset Here</Link>.
					</Typography>
				</Stack>
			</Stack>
		</form>
		</Container>
	)
}
