import {<PERSON><PERSON>, <PERSON>, Divider, Stack, Typography} from "@mui/material";
import {useQuery} from "@tanstack/react-query";
import * as React from "react";
import {useEffect, useState} from "react";
import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import {Link, useParams} from "react-router-dom";
import {ColDef} from "ag-grid-community";
import {CustomCellRendererProps} from "ag-grid-react";
import ServerSideDataTable from "@components/ServerSideDataTable";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import IconButton from "@mui/material/IconButton";
import {ArrowBack} from "@mui/icons-material";
import {urls} from "@routes";


interface Contact {
	uid: string
	email: string
	bounced: boolean
	unsubscribed: boolean
}

interface PageData {
	status_code: string
	status_text: string

	list_name: string
	contacts: Contact[]
	attribute_column_headers: string[]
}

// type DisplayMode = "csv" | "stats";


export default function ContactListDetails() {
	const {contactListUID} = useParams();

	const [listName, setListName] = useState<string>("");
	const [contacts, setContacts] = useState<Contact[]>([]);
	const [additionalColumnNames, setAdditionalColumnNames] = useState<string[]>([]);
	// const [displayMode, setDisplayMode] = useState<DisplayMode>("stats");

	// Page Title
	useEffect(() => {
		document.title = "Contact List Details - Deliveryman.ai";
	}, []);

	// Fetch the page data.
	const pageDataQuery = useQuery({
		queryKey: ["emailAccounts"],
		queryFn: () => authenticateAndFetchData(`/contact-list-details/?uid=${contactListUID}`),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			let data = pageDataQuery.data.data as PageData
			setListName(data.list_name);
			setContacts(data.contacts);
			setAdditionalColumnNames(data.attribute_column_headers);
		}
	}, [pageDataQuery.data]);

	// --------------------- TABLE SETUP ---------------------

	const bounceValueFormatter = (customProps: CustomCellRendererProps) => {
		if (customProps.data["bounced"]) {
			return <Typography color={"error"}>Yes</Typography>
		} else {
			return <Typography color={"success"}>No</Typography>
		}
	}

	const unsubscribedValueFormatter = (customProps: CustomCellRendererProps) => {
		if (customProps.data["unsubscribed"]) {
			return <Typography color={"error"}>Yes</Typography>
		} else {
			return <Typography color={"success"}>No</Typography>
		}
	}

	// Columns for contact lists table.
	// const statsModeColumnDefs: ColDef[] = [
	// 	{field: "email", headerName: "Contact Email ID"},
	// 	{field: "uid", headerName: "Contact UID"},
	// 	{
	// 		field: "bounced",
	// 		headerName: "Bounced",
	// 		cellRenderer: bounceValueFormatter,
	// 		sortable: false,
	// 	},
	// 	{
	// 		field: "unsubscribed",
	// 		headerName: "Unsubscribed",
	// 		cellRenderer: unsubscribedValueFormatter,
	// 		sortable: false,
	// 		resizable: false
	// 	}
	// ]
	//
	// const csvModeColumnDefs: ColDef[] = [
	// 	{field: "email", headerName: "Contact Email ID"},
	// ]

	const colDefs: ColDef[] = [
		{field: "email", headerName: "Contact Email ID"},
		{field: "uid", headerName: "Contact UID"},
	]

	if (additionalColumnNames) {
		additionalColumnNames.slice(0, 4).forEach(columnName => {
			colDefs.push({field: columnName, headerName: columnName})
		});
	}

	colDefs.push({
		field: "bounced",
		headerName: "Bounced",
		cellRenderer: bounceValueFormatter,
		sortable: false,
	})

	colDefs.push({
		field: "unsubscribed",
		headerName: "Unsubscribed",
		cellRenderer: unsubscribedValueFormatter,
		sortable: false,
		resizable: false
	})

	// const TableDisplayModeSelector = () => {
	// 	return (
	// 		<Stack direction={"row"} spacing={1}>
	// 			<Button variant={"contained"} size={"small"} onChange={() => setDisplayMode("stats")}>
	// 				Stats Mode
	// 			</Button>
	// 			<Button variant={"contained"} size={"small"} onChange={() => setDisplayMode("csv")}>
	// 				CSV Mode
	// 			</Button>
	// 		</Stack>
	// 	)
	// }

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading || pageDataQuery.isRefetching) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else {
		return (
			<Stack direction={"column"} spacing={4} sx={{pb: 4, height: "100%"}}>
				<Stack direction={"column"} spacing={1}>
					<Stack direction={"row"} spacing={2} alignItems={"center"}>
						<IconButton component={Link} to={urls["contactLists"]}>
							<ArrowBack/>
						</IconButton>
						<Typography variant={"h6"} align={"left"}>
							Contact List: {listName}
						</Typography>
					</Stack>
					<Divider sx={{mt: 2, mb: 2}}/>
				</Stack>
				<Alert severity={"info"} sx={{mb: 2}}>
					Upto 20 columns are imported from your CSV. However, only first 5 columns can be shown below.
				</Alert>
				<Box sx={{height: "100%"}}>
					<ServerSideDataTable columns={colDefs}
															 rows={contacts}/>
				</Box>
			</Stack>
		);
	}
}
