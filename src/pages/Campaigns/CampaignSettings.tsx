import {
	Box,
	Button,
	Checkbox,
	Chip,
	FormControlLabel,
	List,
	ListItemText,
	MenuItem,
	Select,
	Stack,
	TextField,
	Typography
} from "@mui/material";
import {useEffect, useState, useRef } from "react";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import {Save} from "@mui/icons-material";
import {useSnackbar} from "notistack";
import CampaignDomainSelection, {CDSManagedSubdomain} from "@components/CampaignDomainSelection";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import CampaignSkipDaysSelection from "@components/CampaignSkipDaysSelection";

interface PageData {
	status_code: string
	status_text: string

	reply_to_address: string
	max_emails_daily: number
	managed_subdomains: CDSManagedSubdomain[]
	skip_days: Array<string>
	test_email_target: Array<string>

	is_html_email: boolean
	add_unsub_link: boolean
	unsub_link_type: "auto" | "custom"
	custom_unsub_text: string
}

const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];

export default function CampaignSettings(props: {
	campaignUID: string
}) {
	const {enqueueSnackbar} = useSnackbar();

	const [replytoAddress, setReplytoAddress] = useState<string>("");
	const [testEmailTarget, setTestEmailTarget] = useState<string>("");
	const [unsubLinkEnabled, setUnsubLinkEnabled] = useState<boolean>(false);
	const [unsubLinkType, setUnsubLinkType] = useState<string>("auto");
	const [customUnsubText, setCustomUnsubText] = useState<string>("");
	const [initialLoadComplete, setInitialLoadComplete] = useState(false);
	const [managedSubdomains, setManagedSubdomains] = useState<CDSManagedSubdomain[]>([]);
	const [skipDays, setSkipDays] = useState<Array<string>>(days);

	const [pageData, setPageData] = useState<PageData>();
	
	const prevValuesRef = useRef<any>(null);
	const formInitialized = useRef(false);

	// Run query to fetch page data.
	const pageDataQuery = useQuery({
		queryKey: ["getCampaignSetting"],
		queryFn: () => authenticateAndFetchData(`/campaigns/settings/?campaign_uid=${props.campaignUID}`),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			const pd = pageDataQuery.data.data as PageData;
			setPageData(pd);
			setReplytoAddress(pd.reply_to_address);
			setManagedSubdomains(pd.managed_subdomains);
			setSkipDays(days.filter(value => !pd.skip_days.includes(value)));
			setTestEmailTarget(pd.test_email_target.join(", "));
			setUnsubLinkEnabled(pd.add_unsub_link);
			setUnsubLinkType(pd.unsub_link_type);
			setCustomUnsubText(pd.custom_unsub_text);

			prevValuesRef.current = {
				replytoAddress: pd.reply_to_address,
				testEmailTarget: pd.test_email_target.join(", "),
				unsubLinkEnabled: pd.add_unsub_link,
				unsubLinkType: pd.unsub_link_type,
				customUnsubText: pd.custom_unsub_text,
				skipDays: days.filter(value => !pd.skip_days.includes(value)),
				managedSubdomains: pd.managed_subdomains,
			};
			
			setInitialLoadComplete(true); 
		}
	}, [pageDataQuery.data]);

	// --------------- Muatation to save changes ---------------
	const saveChangesMutation = useMutation({
		mutationKey: ["saveCampaignSettings"],
		mutationFn: (data: {
			campaignUID: string,
			replyToAddress: string
			testEmailArray: string[]
		}) => authenticateAndPostData("/campaigns/settings/", {
			campaign_uid: data.campaignUID,
			reply_to_address: data.replyToAddress,
			selected_domain_ids: managedSubdomains
				.filter(value => value.checked)
				.map(value => value.id),
			skip_days: days.filter(value => !skipDays.includes(value)),
			test_email_target: data.testEmailArray,
			add_unsub_link: unsubLinkEnabled,
			unsub_link_type: unsubLinkType,
			custom_unsub_text: customUnsubText,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: (response) => {
			enqueueSnackbar("All changes have been saved successfully!", {
				variant: "success",
			});
		},
		onError: (err: ApiRequestFailed) => {
			enqueueSnackbar(err.data.message, {
				variant: "error",
			});
		}
	});

	useEffect(() => {
		if (initialLoadComplete && !formInitialized.current) {
			formInitialized.current = true;
		}
	}, [initialLoadComplete]);

	const handleFieldSave = (overrideFields: Partial<typeof prevValuesRef.current> = {}) => {
		if (!formInitialized.current) return;

		const currentValues = {
			replytoAddress,
			testEmailTarget,
			unsubLinkEnabled,
			unsubLinkType,
			customUnsubText,
			skipDays,
			managedSubdomains,
			...overrideFields,
		};		
		const prevValues = prevValuesRef.current;

		const hasChanged = JSON.stringify(prevValues) !== JSON.stringify(currentValues);		

		if (!hasChanged || !replytoAddress || customUnsubText.length > 100) return;

		const testEmailArray = testEmailTarget
			.trim()
			.split(",")
			.filter((email) => email)
			.slice(0, 5)
			.map((email) => email.trim());

		saveChangesMutation.mutate({
			campaignUID: props.campaignUID,
			replyToAddress: replytoAddress,
			testEmailArray,
		});

		prevValuesRef.current = currentValues;
	};



	// function saveAllChanges() {
	// 	if (customUnsubText.length > 100) {
	// 		enqueueSnackbar("Save Failed: Please keep custom unsubscribe text less than 100 characters long.", {
	// 			variant: "error",
	// 		});
	// 		return;
	// 	}

	// 	if (!replytoAddress) {
	// 		enqueueSnackbar("Save Failed: Please make sure all required fields are filled in.", {
	// 			variant: "error",
	// 		});
	// 		return;
	// 	}

	// 	// Limit test email destinations.
	// 	let testEmailArray = testEmailTarget
	// 		.trim()
	// 		.split(",")
	// 		.filter(value => value)
	// 		.slice(0, 5)
	// 		.map(email => email.trim());

	// 	saveChangesMutation.mutate({
	// 		campaignUID: props.campaignUID,
	// 		replyToAddress: replytoAddress,
	// 		testEmailArray: testEmailArray,
	// 	});
	// }

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction="column" sx={{pb: 6}} spacing={6}>
				{/* ------------------------ Reply-To Address ------------------------ */}
				<Box display="flex" flexDirection="column" justifyContent="start" alignItems="start">
					<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
						<Box component="span" sx={{color: "error.main"}}>*</Box>
						&nbsp;Where do you want to receive all replies on?
					</Typography>
					<TextField id="domain"
										 placeholder="<EMAIL>"
										 value={replytoAddress}
										 onChange={e => setReplytoAddress(e.target.value)}
										 onBlur={() => handleFieldSave()}
										 variant="outlined"
										 sx={{mt: 1, width: {sm: "100%", md: "450px"}}}/>
					<Typography variant={"caption"} sx={{mt: 1}}>
						This email will be used as the destination mailbox for all replies. Cannot be empty.
					</Typography>
				</Box>

				{/* ------------------------ Test Email Destinations ------------------------ */}
				<Box display="flex" flexDirection="column" justifyContent="start" alignItems="start">
					<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
						Add one or more test email destinations (Max 5 emails)
					</Typography>
					<TextField id="testEmailDestinations"
										 value={testEmailTarget}
										 onChange={e => setTestEmailTarget(e.target.value)}
										 onBlur={() => handleFieldSave()}
										 variant="outlined"
										 sx={{mt: 1, width: {sm: "100%", md: "450px"}}}/>
					<List>
						<ListItemText primaryTypographyProps={{variant: "caption"}}>
							Separate multiple emails with comma.
						</ListItemText>
						<ListItemText primaryTypographyProps={{variant: "caption"}}>
							If left empty, we'll use your account email address by default.
						</ListItemText>
						<ListItemText primaryTypographyProps={{variant: "caption"}}>
							Only Max. 5 emails will be considered.
						</ListItemText>
					</List>
				</Box>

				{/* ------------------------ Unsubscribe Link ------------------------ */}
				<Box display="flex" flexDirection="column" justifyContent="start" alignItems="start">
					<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
						Add unsubscribe text to campaign emails?
					</Typography>
					<Typography variant={"body2"}>
						If enabled, we'll add unsubscribe text to the end of every message
					</Typography>
					<FormControlLabel
						control={<Checkbox checked={unsubLinkEnabled} onChange={(e) => {setUnsubLinkEnabled(e.target.checked); handleFieldSave({ unsubLinkEnabled: e.target.checked });;}}/>}
						label={"Add Unsubscribe Text"}
						sx={{mt: 1}}/>
					{unsubLinkEnabled &&
              <Stack direction={"column"} spacing={2} sx={{mt: 1}}>
                  <Select
                      value={unsubLinkType}
                      onChange={(e) => {
						setUnsubLinkType(e.target.value);
						handleFieldSave({ unsubLinkType: e.target.value });
						}}
                      sx={{width: 200}}
                  >
                      <MenuItem value={"auto"}>Auto-generate</MenuItem>
                      <MenuItem value={"custom"}>Custom Text</MenuItem>
                  </Select>

								{unsubLinkType === "custom" && <Stack direction={"column"} spacing={1}>
                    <Stack direction={"column"}>
                        <Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
                            Custom Unsubscribe Text:
                        </Typography>
											{/*<Typography variant={"body2"}>*/}
											{/*    This will be used in the following format: {'<Your Custom Text> "Unsubscribe"'}*/}
											{/*</Typography>*/}
                    </Stack>
                    <TextField id="unsubCustomText"
                               value={customUnsubText}
                               onChange={e => setCustomUnsubText(e.target.value)}
							   onBlur={() => handleFieldSave()}
                               variant="outlined"
                               sx={{width: {sm: "100%", md: "450px"}}}/>
                    <Typography variant={"caption"}>{customUnsubText} "Unsubscribe"</Typography>
                </Stack>}
              </Stack>}
				</Box>

				{/* ------------------------ Current Selected Domains ------------------------ */}
				<Stack direction={"column"} spacing={1}>
					<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
						The following domain will be used for sending the email campaign:
					</Typography>
					<Stack direction={"row"} spacing={1} style={{maxWidth: "600px"}} flexWrap="wrap" useFlexGap>
						{pageData.managed_subdomains.filter(msub => {
							return msub.checked
						}).map(msub => <Chip label={msub.domain} size={"small"} key={msub.id}/>)}
					</Stack>
					<Typography variant={"body2"}>
						Based on the current domain reputation, you will be able to send a
						maximum of {pageData.max_emails_daily} emails daily.
					</Typography>
				</Stack>


				{/* ------------------------ Managed subdomain selection ------------------------ */}
				<CampaignDomainSelection items={managedSubdomains}
																 onChange={(value: CDSManagedSubdomain[]) => {
																	 setManagedSubdomains(value);
																	  handleFieldSave({ managedSubdomains: value });
																 }}/>

				<CampaignSkipDaysSelection skipDays={skipDays}
																	 onChange={value => {
																		 setSkipDays(value);
																		  handleFieldSave({ skipDays: value }); 
																	 }}/>

				{/* ------------------------ Save Button ------------------------ */}
				{/* <Box>
					<Button variant={"contained"}
									disabled={saveChangesMutation.isPending}
									onClick={saveAllChanges}>
						{saveChangesMutation.isPending ?
							<><Save sx={{mr: 1}}/>Saving...</> :
							<><Save sx={{mr: 1}}/>Save All Changes</>}
					</Button>
				</Box> */}
			</Stack>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
