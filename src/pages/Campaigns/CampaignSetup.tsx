import {
	Accordion,
	AccordionDetails,
	AccordionSummary,
	Box,
	Button,
	Card,
	CardContent,
	Chip,
	MenuItem,
	Select,
	Stack,
	TextField,
	Tooltip,
	Typography
} from "@mui/material";
import {AddCircle, ArrowDropDown, Delete, KeyboardArrowDown, Save, Send} from "@mui/icons-material";
import {useParams} from "react-router-dom";
import React, {Dispatch, useEffect, useReducer, useState} from "react";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";

import equal from 'fast-deep-equal/react';
import {useSnackbar} from "notistack";
import 'react-quill/dist/quill.snow.css';
import SpamRateScore from "@components/SpamRateScore";
import EmailHowToInfoAlert from "@components/EmailHowToInfoAlert";
import {useDialogs} from "@toolpad/core";
import VariableSelectionMenu from "@components/VariableSelectionMenu";
import InsertLinkButton from "@components/InsertLinkButton";
import InsertBoldTextButton from "@components/InsertBoldTextButton";
import InsertItalicTextButton from "@components/InsertItalicTextButton";
import InsertLineBreakButton from "@components/InsertLineBreakButton";


// ------------------ CREATE EMAIL REDUCER ------------------

type ContentType = "text/plain" | "text/html";

interface Email {
	uid: string
	subject: string;
	body: string
	next_message_days: number
	score: number | null
	verdict: string | null
	suggestions: Array<string>
	approval_request_flag: boolean
	label: string | null
	content_type: ContentType
}

interface PageData {
	status_code: number
	status_message: string

	variables: Array<string>
	email_messages: Array<Email>
}

interface GenerateMessageUIDResponse {
	status_code: number
	status_message: string

	uid: string
}

type State = {
	emails: Email[],
}

type CampaignSetupProps = {
	onUnsavedChangesChange?: (hasChanges: boolean) => void;
};

type LoadDataAction = { type: "LOAD_DATA", emailData: Email[] };
type AddEmailAction = {
	type: "ADD_EMAIL",
	uid: string,
	subject: string,
	body: string,
	nextMessageDays: number,
};
type UpdateContentTypeAction = { type: "UPDATE_CONTENT_TYPE", content_type: ContentType, index: number };
type UpdateSubjectAction = { type: "UPDATE_SUBJECT", subject: string, index: number };
type UpdateEmailBodyAction = { type: "UPDATE_EMAIL_BODY", emailBody: string, index: number };
type UpdateDaysAction = { type: "UPDATE_DAYS", days: number, index: number };
type DeleteEmailAction = { type: "DELETE_EMAIL", uid: string };

type Action = LoadDataAction | AddEmailAction | UpdateContentTypeAction | UpdateSubjectAction | UpdateEmailBodyAction |
	UpdateDaysAction | DeleteEmailAction;

function reducer(emailData: State, action: Action): State {
	switch (action.type) {
		case "LOAD_DATA":
			return {
				emails: action.emailData,
			};

		case "ADD_EMAIL":
			return {
				...emailData,
				emails: [...emailData.emails, {
					uid: action.uid,
					subject: action.subject,
					body: action.body,
					next_message_days: action.nextMessageDays,
					score: null,
					verdict: null,
					suggestions: [],
					approval_request_flag: false,
					label: null,
					content_type: "text/plain",
				}]
			}

		case "DELETE_EMAIL":
			return {
				...emailData,
				emails: emailData.emails.filter(email => email.uid !== action.uid)
			}

		case "UPDATE_CONTENT_TYPE":
			return {
				...emailData,
				emails: emailData.emails.map((email, index) => {
					if (index === action.index) {
						return {...email, content_type: action.content_type};
					} else {
						return email;
					}
				})
			}

		case "UPDATE_SUBJECT":
			return {
				...emailData,
				emails: emailData.emails.map((email, index) => {
					if (index === action.index) {
						return {...email, subject: action.subject}
					} else {
						return email
					}
				}),
			}

		case "UPDATE_EMAIL_BODY":
			return {
				...emailData,
				emails: emailData.emails.map((email, index) => {
					if (index === action.index) {
						return {...email, body: action.emailBody}
					} else {
						return email
					}
				}),
			}

		case "UPDATE_DAYS":
			return {
				...emailData,
				emails: emailData.emails.map((email, index) => {
					if (index === action.index) {
						return {...email, next_message_days: action.days}
					} else {
						return email
					}
				}),
			}

		default:
			throw new Error("Unknown action");
	}
}


export default function CampaignSetup({onUnsavedChangesChange}: CampaignSetupProps) {
	const {enqueueSnackbar} = useSnackbar();
	const {campaignUID} = useParams();
	const [emailData, dispatch] = useReducer(reducer, {emails: []});

	const [initialData, setInitialData] = useState<Email[]>();
	const [saveDisabled, setSaveDisabled] = useState<boolean>(false);
	const [error, setError] = useState(false);

	const [variables, setVariables] = useState<Array<string>>([]);

	// Fetch page data.
	const pageDataQuery = useQuery({
		queryKey: ["campaignSetupPageData"],
		queryFn: () => authenticateAndFetchData(`/campaigns/setup-emails?campaign_uid=${campaignUID}`),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			let data = pageDataQuery.data.data as PageData;
			setVariables(data.variables);
			setInitialData(data.email_messages);
			dispatch({
				type: "LOAD_DATA",
				emailData: data.email_messages,
			});
		}
	}, [pageDataQuery.data]);

	useEffect(() => {
		if (!initialData) {		
			return;
		}
		const isEqual = equal(emailData.emails, initialData);
		setSaveDisabled(isEqual);
		onUnsavedChangesChange?.(!isEqual);
	}, [initialData, emailData.emails]);

	// Mutation to add a new campaign message.
	const addEmailMutation = useMutation({
		mutationKey: ["generateNewMessageUID"],
		mutationFn: () => authenticateAndPostData("/campaigns/generate-message-uid/", {}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: (response) => {
			let responseData = response.data as GenerateMessageUIDResponse;
			dispatch({
				type: "ADD_EMAIL",
				uid: responseData.uid,
				subject: "",
				body: "",
				nextMessageDays: 1,
			});
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	// Mutation to save changes.
	const saveChangesMutation = useMutation({
		mutationKey: ["saveCampaignSetupChanges"],
		mutationFn: (
			emailData: Array<Email>
		) => authenticateAndPostData("/campaigns/save-campaign-setup-changes/", {
			campaign_uid: campaignUID,
			email_data: emailData
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			pageDataQuery.refetch().then();
			enqueueSnackbar("All email changes saved successfully!", {
				variant: "success",
			});
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
				autoHideDuration: 10000,
				style: {whiteSpace: "pre-line"}
			});
		}
	})

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<Box sx={{flexGrow: 1, pb: 6}}>
			{/* ----------- Header Controls ----------- */}
			<Stack direction={"row"} spacing={2}>
				<EmailHowToInfoAlert sx={{width: "100%"}}/>
				<Button variant={"contained"}
								startIcon={<Save/>}
								color={"primary"}
								disabled={error || saveDisabled || saveChangesMutation.isPending}
								sx={{width: "250px"}}
								onClick={() => {
									saveChangesMutation.mutate(emailData.emails);
								}}>
					{saveChangesMutation.isPending ? "Saving Changes..." : "Save Changes"}
				</Button>
			</Stack>
			{/* ----------- Email Cards ----------- */}
			<Box display={"flex"} flexDirection={"column"} alignItems={"center"} justifyContent={"center"} sx={{mt: 4}}>
				<Stack direction={"column"} spacing={2} sx={{width: "100%"}}>
					{emailData.emails.map((email, index) => (
						<StepCard key={index}
											campaignUID={campaignUID!}
											messageUID={email.uid}
											index={index}
											variables={variables}
											subject={email.subject}
											emailBody={email.body}
											nextEmailDays={email.next_message_days}
											score={email.score}
											verdict={email.verdict}
											suggestions={email.suggestions.length > 0 ? email.suggestions : ["Everything looks good!"]}
											label={email.label}
											content_type={email.content_type}
											approval_request_flag={email.approval_request_flag}
											dispatch={dispatch}
											error={error}
											setError={setError}/>
					))}
				</Stack>
				{/* ----------- Add Email Button ----------- */}
				<Button variant={"contained"}
								startIcon={<AddCircle/>}
								color={"primary"}
								sx={{mt: 2}}
								onClick={() => {
									addEmailMutation.mutate();
								}}>
					Add Email
				</Button>
			</Box>
		</Box>
	)
}

function StepCard(props: {
	campaignUID: string,
	messageUID: string,
	index: number,
	variables: Array<string>,
	subject: string,
	emailBody: string,
	nextEmailDays: number,
	score: number | null,
	verdict: string | null,
	suggestions: Array<string>,
	label: string | null,
	content_type: ContentType,
	approval_request_flag: boolean,
	dispatch: Dispatch<Action>,
	error: boolean,
	setError: React.Dispatch<React.SetStateAction<boolean>>,
}) {
	const {enqueueSnackbar} = useSnackbar();
	const dialogs = useDialogs();

	const [approvalRequestSent, setApprovalRequestSent] = useState(false);

	function scoreBadgeColor(score: number) {
		if (score < 75) {
			return "error";
		} else if (score < 85) {
			return "warning";
		} else {
			return "success";
		}
	}

	function labelBadgeColor(label: string) {
		if (label === "passed" || label === "approved") {
			return "success";
		} else {
			return "error";
		}
	}

	function getLabelTooltipContent(label: string) {
		if (label === "passed") {
			return "Everything's good to go!.";

		} else if (label === "approved") {
			return "Your message content has been approved during manual verification."

		} else if (label === "blocked") {
			return "Our system has classified this message as inappropriate (spam/scam/spoofing/phishing). Please edit this message or request Manual Approval to have one of our team members verify it.";

		} else {
			return "We have confirmed the presence of inappropriate content during manual verification.";
		}
	}

	const sendTestEmailMutation = useMutation({
		mutationKey: ["sendTestEmail"],
		mutationFn: () => authenticateAndPostData("/send-test-email/", {
			"uid": props.campaignUID,
			"subject": props.subject,
			"body": props.emailBody,
			"content_type": props.content_type,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: (data) => {
			enqueueSnackbar(`Test email sent successfully from ${data.data["email_used"]}`, {
				variant: "success",
			});
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	const sendApprovalRequestMutation = useMutation({
		mutationKey: ["sendApprovalRequest"],
		mutationFn: () => authenticateAndPostData("/campaigns/request-content-approval/", {
			"message_uid": props.messageUID,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			setApprovalRequestSent(true);
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message);
		}
	})

	function addVariable(value: string) {
		props.dispatch({
			type: "UPDATE_EMAIL_BODY",
			emailBody: props.emailBody + "{{" + value + "}}",
			index: props.index
		});
	}

	function handleInsertLink(text: string, link: string) {
		props.dispatch({
			type: "UPDATE_EMAIL_BODY",
			emailBody: props.emailBody + ` <a href="${link}">${text}</a> `,
			index: props.index
		});
	}

	function handleInsertBoldText(text: string) {
		props.dispatch({
			type: "UPDATE_EMAIL_BODY",
			emailBody: props.emailBody + ` <b>${text}</b> `,
			index: props.index
		});
	}

	function handleInsertItalicText(text: string) {
		props.dispatch({
			type: "UPDATE_EMAIL_BODY",
			emailBody: props.emailBody + ` <i>${text}</i> `,
			index: props.index
		});
	}

	function addLineBreakTag() {
		props.dispatch({
			type: "UPDATE_EMAIL_BODY",
			emailBody: props.emailBody + `<br/>`,
			index: props.index
		});
	}

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const value = e.target.value;
		const regex = /^[a-zA-Z0-9\s/+]*$/;

		if (regex.test(value)) {
			props.setError(false);
			props.dispatch({
				type: "UPDATE_SUBJECT",
				subject: value,
				index: props.index
			});
		} else {
			props.dispatch({
				type: "UPDATE_SUBJECT",
				subject: value,
				index: props.index
			});
			props.setError(true);
		}
	};

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<Stack direction={"column"} alignItems={"center"}>
			<Card sx={{
				width: "100%",
			}}>
				<CardContent sx={{
					'&.MuiCardContent-root': {
						p: 1
					}
				}}>
					<Accordion sx={{border: "none", boxShadow: "none"}}>
						<AccordionSummary expandIcon={<ArrowDropDown/>}>
							<Box display={"flex"} flexDirection={"row"} alignItems={"center"}>
								<Typography variant={"h6"}>
									Email {props.index + 1} - {props.subject ? props.subject : "No Subject"}
								</Typography>
								{props.score !== null && <Chip variant={"filled"}
                                               label={`Email Score: ${props.score}%`}
                                               size={"small"}
                                               color={scoreBadgeColor(props.score)}
                                               sx={{fontWeight: "bold", ml: 4}}/>}
								{props.label !== null && <Tooltip title={getLabelTooltipContent(props.label)}>
                    <Chip variant={"filled"}
                          label={`Content Check: ${props.label}`}
                          size={"small"}
                          color={labelBadgeColor(props.label)}
                          sx={{fontWeight: "bold", ml: 4}}/>
                </Tooltip>}
								{(props.label === "blocked" || (props.score !== null && props.score < 75)) &&
                    <Tooltip title={"Request manual verification from our team. NOTE: " +
											"In case the message is edited and saved at any point, this request will be cancelled."}>
                        <Button variant={"outlined"}
                                size={"small"}
                                sx={{ml: 4}}
                                disabled={props.approval_request_flag || approvalRequestSent || sendApprovalRequestMutation.isPending}
                                onClick={(event) => {
																	event.stopPropagation();
																	sendApprovalRequestMutation.mutate();
																}}>
													{(props.approval_request_flag || approvalRequestSent) ? "Waiting For Approval" : "Request Approval"}
                        </Button>
                    </Tooltip>}
							</Box>
						</AccordionSummary>
						<AccordionDetails>
							<Stack direction={"column"} spacing={2}>
								<Typography variant="body1" fontWeight={"bold"}>
									Select Email Content Type
								</Typography>
								<Select value={props.content_type}
												onChange={e => {
													props.dispatch({
														type: "UPDATE_CONTENT_TYPE",
														content_type: e.target.value as ContentType,
														index: props.index
													});
												}}
												sx={{maxWidth: 200}}>
									<MenuItem value={"text/plain"}>Plain Text</MenuItem>
									<MenuItem value={"text/html"}>HTML</MenuItem>
								</Select>
							</Stack>
							<Stack direction={"column"} spacing={2} sx={{mt: 2}}>
								<Typography variant="body1" fontWeight={"bold"}>
									Provide email subject & body
								</Typography>
								<TextField value={props.subject}
													 label={"Subject"}
													 placeholder={"Subject"}
													 onChange={handleChange}
													 error={props.error}
													 helperText={props.error ? "Invalid characters used. Only / + allowed." : ""}
													 fullWidth required/>
								<Stack direction={"column"} spacing={2}>
									<Stack direction={"row"} spacing={2}>
										<VariableSelectionMenu options={props.variables}
																					 onClickHandler={addVariable}
																					 maxMenuHeight={300}/>
										{/* -------------- Only show these buttons for HTML -------------- */}
										{props.content_type === "text/html" && <InsertLinkButton onApply={handleInsertLink}/>}
										{props.content_type === "text/html" && <InsertBoldTextButton onApply={handleInsertBoldText}/>}
										{props.content_type === "text/html" && <InsertItalicTextButton onApply={handleInsertItalicText}/>}
										{props.content_type === "text/html" && <InsertLineBreakButton onClick={addLineBreakTag}/>}
									</Stack>
									<TextField
										placeholder="Write your email body here..."
										value={props.emailBody}
										onChange={(e) => {
											props.dispatch({
												type: "UPDATE_EMAIL_BODY",
												emailBody: e.target.value,
												index: props.index
											});
										}}
										multiline
										rows={15}
										slotProps={{htmlInput: {style: {resize: 'vertical'}}}}
									/>
								</Stack>
								<Box display={"flex"} justifyContent={"space-between"} alignItems={"flex-start"} flexWrap={"wrap"}>
									{props.score !== null && <Box sx={{width: "50%"}}>
                      <SpamRateScore
                          score={props.score}
                          verdict={props.verdict}
                          suggestions={props.suggestions}
                          sx={{mt: 4}}
                      />
                  </Box>}
									<Stack direction={"column"}>
										<Box display="flex" alignItems="center">
											<Typography>Next email in line will be sent in</Typography>
											<TextField
												value={props.nextEmailDays.toString()}
												type={"number"}
												aria-valuemin={1}
												variant="outlined"
												size="small"
												sx={{mx: 1, width: '100px'}}
												placeholder="Days"
												onChange={e => {
													props.dispatch({
														type: "UPDATE_DAYS",
														days: parseInt(e.target.value),
														index: props.index
													});
												}}
											/>
											<Typography>days</Typography>
										</Box>
										<Stack sx={{mt: 4}} direction={"row"} spacing={2}>
											<Button variant={"outlined"}
															color={"error"}
															startIcon={<Delete/>}
															onClick={() => {
																props.dispatch({
																	type: "DELETE_EMAIL",
																	uid: props.messageUID,
																});
															}}>
												Delete Email
											</Button>
											<Button variant={"outlined"}
															color={"success"}
															startIcon={<Send/>}
															disabled={!props.subject || !props.emailBody || sendTestEmailMutation.isPending}
															onClick={async () => {
																const sendTestEmail = await dialogs.confirm(
																	"Send a test email with this subject and body? " +
																	"All variables will be replaced with data from a random contact.",
																	{
																		title: "Send Test Email",
																		cancelText: "Cancel",
																		okText: "Yes",
																	}
																);
																if (sendTestEmail) {
																	sendTestEmailMutation.mutate();
																}
															}}>
												{sendTestEmailMutation.isPending ? "Sending Test Email..." : "Send Test Email"}
											</Button>
										</Stack>
									</Stack>
								</Box>
							</Stack>
						</AccordionDetails>
					</Accordion>
				</CardContent>
			</Card>
			<KeyboardArrowDown sx={{mt: 2}} fontSize={"large"}/>
		</Stack>
	);
}
