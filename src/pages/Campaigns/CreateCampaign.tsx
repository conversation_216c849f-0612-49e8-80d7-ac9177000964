import {<PERSON>, <PERSON><PERSON>, <PERSON>ir<PERSON><PERSON><PERSON><PERSON>, <PERSON>ack, TextField, Typography, useTheme} from "@mui/material";
import {ArrowBack, Close, NavigateNext, OpenInNew, Refresh} from "@mui/icons-material";
import {ChangeEvent, useEffect, useState} from "react";
import {urls} from "@routes";
import {Link, useNavigate} from "react-router-dom";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import {useSnackbar} from "notistack";
import CampaignDomainSelection, {CDSManagedSubdomain} from "@components/CampaignDomainSelection";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import CampaignSkipDaysSelection from "@components/CampaignSkipDaysSelection";
import {CustomCellRendererProps} from "ag-grid-react";
import {ColDef, ValueFormatterParams} from "ag-grid-community";
import {format} from "date-fns";
import ServerSideDataTable from "@components/ServerSideDataTable";


interface ContactList {
	uid: string
	name: string
	created_on: number
	total_contacts: number
	total_columns: number
}

interface PageData {
	status_code: string
	status_text: string

	campaigns_blocked: boolean
	managed_subdomains: CDSManagedSubdomain[]
	contact_lists: ContactList[]
}

interface ContactListQueryResult {
	status_code: string
	status_text: string

	contact_lists: ContactList[]
}

const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];


export default function CreateCampaign() {
	const navigate = useNavigate();
	const {enqueueSnackbar} = useSnackbar();
	const theme = useTheme();

	const [pageData, setPageData] = useState<PageData>();
	const [contactLists, setContactLists] = useState<ContactList[]>([]);
	const [campaignName, setCampaignName] = useState<string>("");
	const [selectedContactLists, setSelectedContactLists] = useState<ContactList[]>([]);
	const [emailsPerDay, setEmailsPerDay] = useState<string>("");
	const [replyToAddress, setReplyToAddress] = useState<string>("");
	const [
		selectedDomains,
		setSelectedDomains
	] = useState<CDSManagedSubdomain[]>([]);
	const [skipDays, setSkipDays] = useState<Array<string>>(days);

	const [proceedButtonDisabled, setProceedButtonDisabled] = useState<boolean>(true);


	// Fetch page data.
	const pageDataQuery = useQuery({
		queryKey: ["createCampaignPageData"],
		queryFn: () => authenticateAndFetchData("/campaigns/create/"),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			let data = pageDataQuery.data.data as PageData
			setPageData(data);
			setContactLists(data.contact_lists);
		}
	}, [pageDataQuery.data]);

	// Query to fetch just the contact list data. Won't fire on page load.
	const contactListsQuery = useQuery({
		queryKey: ["contactListsQuery"],
		queryFn: () => authenticateAndFetchData("/campaigns/create/fetch-contact-lists/"),
		gcTime: 0,
		retry: retryFn,
		enabled: false,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (contactListsQuery.data) {
			let data = contactListsQuery.data.data as ContactListQueryResult
			setContactLists(data.contact_lists);
		}
	}, [contactListsQuery.data]);

	// Unlocks proceed button if condition is satisfied.
	useEffect(() => {
		if (campaignName && (selectedContactLists.length > 0)) {
			setProceedButtonDisabled(false);
		} else {
			setProceedButtonDisabled(true);
		}
	}, [campaignName, selectedContactLists]);

	// Mutation to create campaign.
	const createCampaignMutation = useMutation({
		mutationKey: ["createCampaignMutation"],
		mutationFn: () => authenticateAndPostData("/campaigns/create/", {
			"name": campaignName,
			"emails_per_day": emailsPerDay,
			"reply_to_address": replyToAddress,
			"selected_contact_list_uids": selectedContactLists.map(value => value.uid),
			"selected_domain_ids": selectedDomains
				.filter(value => value.checked)
				.map(value => value.id),
			"skip_days": days.filter(value => !skipDays.includes(value)),
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			navigate(urls["campaigns"]);
		},
		onError: (error: ApiRequestFailed) => {
			console.log(error);
			enqueueSnackbar(
				error.data.message || "Something went wrong on our side. Please try again in some time.",
				{
					variant: "error",
				});
		}
	});

	function emailsPerDayHandler(event: ChangeEvent<HTMLInputElement>) {
		// Only allow numbers.
		const newValue = event.target.value;
		if (newValue === '' || /^[0-9\b]+$/.test(newValue)) {
			setEmailsPerDay(newValue);
		}
	}

	function submitHandler() {
		if (selectedDomains.length === 0) {
			enqueueSnackbar("Please select at least one domain for sending emails in this campaign.", {
				variant: "error",
			});
			return;
		}

		if (campaignName && (selectedContactLists.length > 0)) {
			createCampaignMutation.mutate();

		} else {
			enqueueSnackbar("Please check and fill in all the details and select at least one contact list to use.",
				{
					variant: "error",
				}
			);
		}
	}

	// --------------------- TABLE SETUP ---------------------

	function timestampFormatter(params: ValueFormatterParams) {
		return format(new Date(params.value), "do MMM y, HH:mm:ss (xxx)");
	}

	const ContactsPreviewButton = (customProps: CustomCellRendererProps) => {
		return (
			<Button component={Link}
							to={urls["contactListDetails"].replace(":contactListUID", customProps.data["uid"])}
							target={"_blank"}
							rel={"noreferrer"}
							size={"small"}
							startIcon={<OpenInNew/>}>
				Preview
			</Button>
		)
	}

	// Columns for contact lists table.
	const contactListSelectionTableColumnDefs: ColDef[] = [
		{field: "name", headerName: "Name"},
		{field: "created_on", headerName: "Imported On", valueFormatter: timestampFormatter, flex: 2},
		{field: "total_contacts", headerName: "Total Contacts"},
		{field: "total_columns", headerName: "Total Columns"},
		{cellRenderer: ContactsPreviewButton, resizable: false, sortable: false}
	]

	// Table action buttons.
	const ContactListTableActions = () => (
		<Stack direction={"row"} spacing={2}>
			<Button startIcon={<Refresh/>}
							variant={"contained"}
							size={"small"}
							disabled={contactListsQuery.isFetching || pageDataQuery.isFetching}
							onClick={() => contactListsQuery.refetch().then()}>
				{contactListsQuery.isFetching ? "Loading..." : "Refresh"}
			</Button>
			<Button component={Link}
							to={urls["contactLists"] + "?newimport=1"}
							target={"_blank"}
							rel={"noreferrer"}
							startIcon={<OpenInNew/>}
							variant={"contained"}
							size={"small"}>
				Add New Contact List
			</Button>
		</Stack>
	)

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		if (pageData.campaigns_blocked) {
			return (
				<Stack justifyContent={"center"} alignItems={"center"}>
					<Typography variant={"h4"} color={"primary"} align={"center"}>Account Blocked</Typography>
					<Typography variant={"body1"} align={"center"} sx={{mt: 1}}>
						Your ability to create new email campaigns has been temporarily suspended.<br/>
						Please contact support for any further assistance.
					</Typography>
					<Button variant={"contained"}
									startIcon={<ArrowBack/>}
									onClick={() => navigate(-1)}
									sx={{mt: 2}}>
						Go Back
					</Button>
				</Stack>
			)

		} else {
			return (
				<Stack direction={"column"} spacing={4} sx={{pb: 6}}>
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"h4"} color={"primary"}>Create New Campaign</Typography>
						<Typography variant={"subtitle1"}>
							Upload contacts, craft your email, choose your sending domains. We automate everything else for you.
						</Typography>
					</Stack>

					{/* Campaign name */}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
							<span style={{color: theme.palette.error.main}}>*</span>&nbsp;Give a name for your Campaign:
						</Typography>
						<TextField value={campaignName}
											 onChange={e => setCampaignName(e.target.value)}
											 label="Campaign Name"
											 variant="outlined"
											 sx={{maxWidth: "500px"}}
											 required/>
					</Stack>

					{/* Contact list selection table */}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
							<span style={{color: theme.palette.error.main}}>*</span>&nbsp;Select at least one contact list to use:
						</Typography>
						<Box sx={{height: "500px"}}>
							<ServerSideDataTable columns={contactListSelectionTableColumnDefs}
																	 rows={contactLists}
																	 onSelectionChange={(rows) => {
																		 setSelectedContactLists(rows);
																	 }}
																	 rowSelection={{mode: "singleRow"}}
																	 actions={ContactListTableActions()}/>
						</Box>
					</Stack>

					{/* Emails Per Day */}
					<Stack direction={"column"} spacing={1}>
						<Box>
							<Typography variant={"subtitle1"} fontWeight={"bold"}>
								How many leads/contacts should we email per day through this campaign?
							</Typography>
							<Typography variant={"body2"}>
								You can specify a fixed number of emails to be sent per day or you can leave it empty so we can
								automatically handle the best sending limits.
							</Typography>
							{/*<Typography variant={"body2"} sx={{mt: 2}}>*/}
							{/*	(NOTE: Less than provided number of emails might be sent on a day depending on certain factors)*/}
							{/*</Typography>*/}
						</Box>
						<TextField
							type="text"
							value={emailsPerDay}
							onChange={emailsPerDayHandler}
							variant="outlined"
							placeholder={"Auto"}
							slotProps={{
								htmlInput: {
									inputMode: 'numeric',
									pattern: '[0-9]*'
								}
							}}
							sx={{maxWidth: "100px"}}
						/>
					</Stack>

					{/* Reply-To */}
					<Stack direction={"column"} spacing={1}>
						<Box>
							<Typography variant={"subtitle1"} fontWeight={"bold"}>
								Where do you want to receive all replies on?
							</Typography>
							<Typography variant={"body2"}>
								This email will be used as the destination mailbox for all replies.
								Leave empty to use your account email.
							</Typography>
						</Box>
						<TextField
							type="text"
							value={replyToAddress}
							onChange={e => setReplyToAddress(e.target.value)}
							label={"<EMAIL>"}
							variant="outlined"
							sx={{maxWidth: "500px"}}
						/>
					</Stack>

					{/* Managed subdomain selection */}
					<CampaignDomainSelection items={pageData.managed_subdomains}
																	 onChange={(value: CDSManagedSubdomain[]) => {
																		 setSelectedDomains(value);
																	 }}/>

					{/* Campaign Skip Days Selection */}
					<CampaignSkipDaysSelection skipDays={skipDays} onChange={value => setSkipDays(value)}/>

					{/* Actions */}
					<Stack direction={"row"} spacing={4}>
						<Button variant={"outlined"} startIcon={<Close/>}
										onClick={() => {
											navigate(urls["campaigns"]);
										}}>
							Cancel
						</Button>
						<Button variant={"contained"}
										endIcon={createCampaignMutation.isPending ? <CircularProgress size={20} color="inherit"/> :
											<NavigateNext/>}
										disabled={proceedButtonDisabled || createCampaignMutation.isPending}
										onClick={submitHandler}>
							{createCampaignMutation.isPending ? "Uploading Data..." : "Proceed"}
						</Button>
					</Stack>
				</Stack>
			);
		}
	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
