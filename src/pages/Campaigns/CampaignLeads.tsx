import {<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Chip, Tab, Tabs, Tooltip, Typography, useTheme,} from "@mui/material";

import TabContent from "@components/TabContent";
import * as React from "react";
import {SyntheticEvent, useEffect, useState} from "react";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import {enqueueSnackbar} from "notistack";
import {Delete} from "@mui/icons-material";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import PageLoading from "@components/PageLoading";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import {styled} from "@mui/material/styles";
import Dialog from "@mui/material/Dialog";
import {ColDef, ValueFormatterParams} from "ag-grid-community";
import {CustomCellRendererProps} from "ag-grid-react";
import IconButton from "@mui/material/IconButton";
import ServerSideDataTable from "@components/ServerSideDataTable";
import { format } from "date-fns";

interface Schedule {
	id: number
	recipient: string
	sender_email: string
	sender_name: string
	subject: string
	email_status: string
	email_sent_on_ts: string
	email_scheduled_ts: string | null
	reply_received: boolean
	reply_email_s3_key: string | null
}

interface Lead {
	uid: string
	email_id: string
	attributes: object | null
	sending_email: string | null
}

interface IgnoredEmail {
	id: number
	email_id: string
	reason: string
}


export default function CampaignLeads(props: {
	campaignUID: string,
	camapignStatus: string,
}) {
	const [tabValue, setTabValue] = useState<number>(0);
	const [leadsCount, setLeadsCount] = useState(0);
	const [badEmailCount, setBadEmailCount] = useState(0);
	const [schedulesCount, setSchedulesCount] = useState(0);

	// Query to fetch contacts tabs data.
	const getContactsDataQuery = useQuery({
		queryKey: ["getContactsDataQuery"],
		queryFn: () => authenticateAndFetchData(
			`/campaigns/details/get-contacts-tabs-data/?campaign_uid=${props.campaignUID}`
		),
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (getContactsDataQuery.data) {
			setLeadsCount(getContactsDataQuery.data.data["leads_count"] as number)
			setBadEmailCount(getContactsDataQuery.data.data["bad_emails_count"] as number)
			setSchedulesCount(getContactsDataQuery.data.data["schedules_count"] as number)
		}
	}, [getContactsDataQuery.data]);

	const handleTabChange = (event: SyntheticEvent, newValue: number) => {
		setTabValue(newValue);
	};

	return (
		<Box sx={{pb: 4}}>
			{/* Tabs */}
			<Box sx={{borderBottom: 1, borderColor: 'divider'}}>
				<Tabs value={tabValue} onChange={handleTabChange}>
					<Tab label={`Leads (${leadsCount})`}/>
					<Tab label={`Bad Emails / Unsubscribes (${badEmailCount})`}/>
					<Tab label={`Schedules (${schedulesCount})`}/>
				</Tabs>
			</Box>
			{/* Leads Tab */}
			<TabContent value={tabValue} index={0}>
				<Leads campaignUID={props.campaignUID} campaignStatus={props.camapignStatus}/>
			</TabContent>
			{/* Bad Emails Tab */}
			<TabContent value={tabValue} index={1}>
				<BadEmails campaignUID={props.campaignUID}/>
			</TabContent>
			{/* Schedules Tab */}
			<TabContent value={tabValue} index={2}>
				<ScheduleData campaignUID={props.campaignUID}/>
			</TabContent>
		</Box>
	)
}


function Leads(props: {
	campaignUID: string,
	campaignStatus: string,
}) {
	const [tableData, setTableData] = useState<Lead[]>([]);
	const [isDeleting, setIsDeleting] = useState(false);

	// Query to fetch schedule data.
	const getLeadsQuery = useQuery({
		queryKey: ["getLeadsQuery"],
		queryFn: () => authenticateAndFetchData(
			`/campaigns/details/get-leads/?campaign_uid=${props.campaignUID}`
		),
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (getLeadsQuery.data) {
			setTableData(getLeadsQuery.data.data["leads"] as Lead[]);
		}
	}, [getLeadsQuery.data]);

	// Mutation to delete contact (only when campaign is in 'created' state)
	const deleteContactMutation = useMutation({
		mutationKey: ["deleteContactMutation"],
		mutationFn: (contactUID: string) => authenticateAndPostData("/campaigns/contacts/delete/", {
			campaign_uid: props.campaignUID,
			contact_uid: contactUID,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			getLeadsQuery.refetch().then();
			setIsDeleting(false);
		},
		onError: (err: ApiRequestFailed) => {
			console.error(err);
			enqueueSnackbar(err.data.message, {
				variant: "error"
			});
			setIsDeleting(false);
		}
	});

	const DeleteLead = (cellProps: CustomCellRendererProps) => {
		return (
			<Tooltip title={"Remove lead from campaign"}>
				<span>
					<IconButton color={"error"}
											disabled={props.campaignStatus !== "created" || deleteContactMutation.isPending || isDeleting}
											onClick={() => {
												setIsDeleting(true);
												deleteContactMutation.mutate(cellProps.data["uid"]);
											}}>
						<Delete/>
					</IconButton>
				</span>
			</Tooltip>
		)
	}

	// Columns for Leads table.
	const columnDefs: ColDef[] = [
		{field: "email_id", headerName: "Email ID"},
		{field: "sending_email", headerName: "Sender Email"},
		{headerName: "Delete", cellRenderer: DeleteLead, type: "rightAligned", resizable: false, maxWidth: 80},
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (getLeadsQuery.isPending || isDeleting) {
		return (
			<PageLoading/>
		)

	} else if (getLeadsQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={getLeadsQuery.error as unknown as ApiRequestFailed}/>

	} else {
		return (
			<Box sx={{height: "680px"}}>
				<ServerSideDataTable columns={columnDefs} rows={tableData}/>
			</Box>
		);
	}
}


function ScheduleData(props: {
	campaignUID: string,
}) {
	const theme = useTheme();
	const [isGeneratingSchedules, setIsGeneratingSchedules] = useState<boolean>(true);
	const [tableData, setTableData] = useState<Schedule[]>([]);

	// Query to fetch schedule data.
	const getSchedulesQuery = useQuery({
		queryKey: ["getSchedulesQuery"],
		queryFn: () => authenticateAndFetchData(
			`/campaigns/details/get-schedules/?campaign_uid=${props.campaignUID}`
		),
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (getSchedulesQuery.data) {
			setIsGeneratingSchedules(getSchedulesQuery.data.data["generating"]);
			setTableData(getSchedulesQuery.data.data["schedules"] as Schedule[]);
		}
	}, [getSchedulesQuery.data]);

	function timestampFormatter(params: ValueFormatterParams) {
		if (params.value) {
			// return formattedDateTime(new Date(params.value));
			return format(new Date(params.value), "do MMM y, HH:mm:ss (xxx)");
		} else {
			return "---";
		}
	}

	const ReplyReceivedCell = (cellProps: CustomCellRendererProps) => {
		if (cellProps.value) {
			return <span style={{color: theme.palette.success.main}}>Yes</span>
		} else {
			return <span>No</span>
		}
	}

	const ViewReplyCell = (cellProps: CustomCellRendererProps) => {
		if (cellProps.data["reply_email_s3_key"]) {
			return (
				<ViewReply replyFrom={cellProps.data["recipient"]}
									 s3key={cellProps.data["reply_email_s3_key"]}/>
			)
		} else {
			return null;
		}
	}

	const EmailStatusCell = (cellProps: CustomCellRendererProps) => {
		switch (cellProps.value) {
			case "created":
				return <Chip label={"Scheduling"} size={"small"} color={"default"} variant={"filled"}/>;

			case "scheduled":
				return <Chip label={"Scheduled"} size={"small"} color={"default"} variant={"filled"}/>;

			case "sent":
				return <Chip label={"Email Sent"} size={"small"} color={"primary"} variant={"filled"}/>;

			case "failed":
				return <Chip label={"Failed"} size={"small"} color={"error"} variant={"filled"}/>;

			case "replied":
				return <Chip label={"Reply Received"} size={"small"} color={"success"} variant={"filled"}/>;

			case "cancelled_reply_received":
				return <Chip label={"Sequence Complete"} size={"small"} color={"success"} variant={"outlined"}/>;

			case "cancelled_unsubscribed":
				return <Chip label={"Unsubscribed"} size={"small"} color={"warning"} variant={"outlined"}/>;

			case "cancelled_campaign_stopped":
				return <Chip label={"Cancelled"} size={"small"} color={"error"} variant={"outlined"}/>;

			case "cancelled_bad_email":
				return <Chip label={"Bad Email"} size={"small"} color={"error"} variant={"outlined"}/>;

			default:
				return <Chip label={"N/A"} size={"small"} color={"default"} variant={"filled"}/>;
		}
	}

	// Columns for Leads table.
	const columnDefs: ColDef[] = [
		{field: "recipient", headerName: "Recipient", flex: 2},
		{field: "sender_email", headerName: "Sender Email", flex: 2},
		{field: "sender_name", headerName: "Sender Name", flex: 2},
		{field: "subject", headerName: "Subject"},
		{field: "email_status", headerName: "Status", cellRenderer: EmailStatusCell},
		{field: "email_scheduled_ts", headerName: "Scheduled For", valueFormatter: timestampFormatter},
		{field: "reply_received", headerName: "Reply Received", cellRenderer: ReplyReceivedCell},
		{field: "view_reply", headerName: "View Reply", cellRenderer: ViewReplyCell, resizable: false},
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (getSchedulesQuery.isPending) {
		return (
			<PageLoading/>
		)

	} else if (getSchedulesQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={getSchedulesQuery.error as unknown as ApiRequestFailed}/>

	} else {
		if (isGeneratingSchedules) {
			return (
				<Box>
					<Alert severity={"info"}>Your schedules are being generated. Please check back in a few minutes.</Alert>
				</Box>
			)

		} else {
			return (
				<Box sx={{height: "680px"}}>
					<ServerSideDataTable columns={columnDefs} rows={tableData}/>
				</Box>
			)
		}
	}
}

function BadEmails(props: {
	campaignUID: string,
}) {
	const [tableData, setTableData] = useState<IgnoredEmail[]>([]);

	// Query to fetch schedule data.
	const getIgnoredEmailsQuery = useQuery({
		queryKey: ["getIgnoredEmails"],
		queryFn: () => authenticateAndFetchData(
			`/campaigns/details/get-ignored-emails/?campaign_uid=${props.campaignUID}`
		),
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (getIgnoredEmailsQuery.data) {
			setTableData(getIgnoredEmailsQuery.data.data["ignored_emails"] as IgnoredEmail[]);
		}
	}, [getIgnoredEmailsQuery.data]);

	// Columns for Leads table.
	const columnDefs: ColDef[] = [
		{field: "email_id", headerName: "Email ID"},
		{field: "reason", headerName: "Reason"},
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (getIgnoredEmailsQuery.isPending) {
		return (
			<PageLoading/>
		)

	} else if (getIgnoredEmailsQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={getIgnoredEmailsQuery.error as unknown as ApiRequestFailed}/>

	} else {
		return (
			<Box sx={{height: "680px"}}>
				<ServerSideDataTable columns={columnDefs} rows={tableData}/>
			</Box>
		);
	}
}


const BootstrapDialog = styled(Dialog)(({theme}) => ({
	'& .MuiDialogContent-root': {
		padding: theme.spacing(2),
	},
	'& .MuiDialogActions-root': {
		padding: theme.spacing(1),
	},
}));


function ViewReply(props: {
	replyFrom: string,
	s3key: string,
}) {
	const [open, setOpen] = React.useState(false);
	const [replyMessage, setReplyMessage] = React.useState("");

	// Query to fetch reply email message.
	const replyMessageQuery = useQuery({
		queryKey: ["replyMessageQuery", props.replyFrom, props.s3key],
		queryFn: () => authenticateAndFetchData(`/campaigns/get-reply-email-message/?key=${props.s3key}`),
		gcTime: 5000,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (replyMessageQuery.data) {
			setReplyMessage(replyMessageQuery.data.data["message"]);
		}
	}, [replyMessageQuery.data]);

	const handleClickOpen = () => {
		setOpen(true);
	};
	const handleClose = () => {
		setOpen(false);
	};

	if (replyMessageQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else {
		return (
			<React.Fragment>
				<Button onClick={handleClickOpen}>
					View Reply
				</Button>
				<BootstrapDialog
					onClose={handleClose}
					aria-labelledby="view-reply-dialog"
					open={open}
				>
					<DialogTitle sx={{m: 0, p: 2}} id="view-reply-dialog">
						Reply from <b>{props.replyFrom}</b>
					</DialogTitle>
					<DialogContent dividers>
						<Typography gutterBottom>
							<pre style={{whiteSpace: "pre-wrap", wordWrap: "break-word"}}>{replyMessage}</pre>
						</Typography>
					</DialogContent>
					<DialogActions>
						<Button autoFocus onClick={handleClose}>
							Close
						</Button>
					</DialogActions>
				</BootstrapDialog>
			</React.Fragment>
		);
	}
}
