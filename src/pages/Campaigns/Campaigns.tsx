import {
	Box,
	<PERSON>ton,
	Chip,
	Dialog,
	<PERSON>alog<PERSON>ctions,
	DialogContent,
	DialogTitle,
	Menu,
	MenuItem,
	Stack,
	TextField,
	ToggleButton,
	ToggleButtonGroup,
	Typography,
	useTheme,
} from "@mui/material";
import React, {useEffect, useRef, useState} from "react";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {useDialogs} from "@toolpad/core";
import {useSnackbar} from "notistack";
import {ColDef, ValueFormatterParams} from "ag-grid-community";
import ServerSideDataTable from "@components/ServerSideDataTable";
import {urls} from "@routes";
import {Link} from "react-router-dom";
import {CustomCellRendererProps} from "ag-grid-react";
import IconButton from "@mui/material/IconButton";
import {Add, Archive, Cancel, ContentCopy, MoreVert} from "@mui/icons-material";
import {format} from "date-fns";
import { formatInTimeZone } from "date-fns-tz";

interface PageData {
	status_code: number
	status_message: string

	campaigns: Campaign[]
	archived_campaigns: Campaign[]
	user_id: number
	timezone: string
}

interface Campaign {
	uid: string
	name: string
	subdomain: string
	created_on_ts: number
	domains: Array<string>
	status: string
	total_emails_sent: number
	total_replies_received: number
	contacts_count: number
}

interface WebhookMessage {
	event_type: string
	event_data: any
}

type StatusFilter = "all" | "ongoing" | "completed" | "cancelled" | "archived";

export default function Campaigns() {
	const dialogs = useDialogs();
	const {enqueueSnackbar} = useSnackbar();
	const ws = useRef<WebSocket | null>(null);
	const theme = useTheme();

	const [statusFilter, setStatusFilter] = useState<StatusFilter>("all")
	const [campaigns, setCampaigns] = useState<Campaign[]>([]);
	const [archivedCampaigns, setArchivedCampaigns] = useState<Campaign[]>([]);
	const [filteredCampaigns, setFilteredCampaigns] = useState<Campaign[]>([]);
	const [userId, setUserId] = useState<number>();
	const [openDuplicateModal, setOpenDuplicateModal] = useState(false);
	const [duplicateTargetCampUID, setDuplicateTargetCampUID] = useState<string>("");
	const [timezone, setTimezone] = useState("")

	// Page Title
	useEffect(() => {
		document.title = "All Campaigns - Deliveryman.ai";
	}, []);

	// Websocket code
	useEffect(() => {
		const wsUrl = `${process.env.REACT_APP_WS_HOST}/ws/campaigns/${userId}/`;

		// Initialize WebSocket connection
		if (userId) {
			ws.current = new WebSocket(wsUrl);

			ws.current.onopen = () => {
				console.log("WebSocket connection established");
			};

			ws.current.onmessage = (event) => {
				const data: WebhookMessage = JSON.parse(event.data)["event_data"];
				const eventName: string = data["event_name"]
				const eventData: any = data["event_data"]

				if (eventName === "campaign_status_update") {
					const campaignUID: string = eventData["campaign_uid"]
					const newStatus: string = eventData["new_status"]
					setCampaigns(prevItems => prevItems.map(campaign => campaign.uid === campaignUID ? {
						...campaign,
						status: newStatus
					} : campaign));
				} else {
					console.log("unhandled ws event:", event);
				}
			};

			ws.current.onclose = () => {
				console.log("WebSocket connection closed");
			};

			ws.current.onerror = (error) => {
				console.error("WebSocket error:", error);
			};
		}

		// Cleanup on component unmount
		return () => {
			if (ws.current) {
				console.log("Cleaning up websocket for closing...");
				ws.current.close();
			}
		};
	}, [userId]);

	// Fetch campaigns data.
	const pageDataQuery = useQuery({
		queryKey: ["campaignPageData"],
		queryFn: () => authenticateAndFetchData("/campaigns/get"),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			let data = pageDataQuery.data.data as PageData;
			setCampaigns(data.campaigns);
			setArchivedCampaigns(data.archived_campaigns);
			setUserId(data.user_id);
			setTimezone(data.timezone)
		}
	}, [pageDataQuery.data]);

	// Update the filtered campaign data when main campaign data changes or the filter changes.
	useEffect(() => {
		if (statusFilter === "all") {
			setFilteredCampaigns(campaigns);

		} else if (statusFilter === "ongoing") {
			setFilteredCampaigns(campaigns.filter(camp => [
				"created", "running", "scheduled"
			].includes(camp.status)));

		} else if (statusFilter === "completed") {
			setFilteredCampaigns(campaigns.filter(camp => camp.status === "complete"));

		} else if (statusFilter === "cancelled") {
			setFilteredCampaigns(campaigns.filter(camp => camp.status === "cancelled"));

		} else {
			// Archived
			setFilteredCampaigns(archivedCampaigns);
		}
	}, [campaigns, archivedCampaigns, statusFilter]);

	// Mutation to duplicate campaign.
	const duplicateCampaignMutation = useMutation({
		mutationKey: ["duplicateCampaignMutation"],
		mutationFn: (data: { name: string, targetCampaignUID: string }) => authenticateAndPostData(
			"/campaigns/duplicate/", {
				"campaign_name": data.name,
				"target_campaign_uid": data.targetCampaignUID,
			}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			pageDataQuery.refetch().then();
			enqueueSnackbar(
				"Campaign has been duplicated successfully!",
				{variant: "success"}
			);
		},
		onError: (error: ApiRequestFailed) => {
			console.log(error);
			enqueueSnackbar(
				error.data.message || "Something went wrong on our side. Please try again in some time.",
				{variant: "error"}
			);
		}
	});

	// Mutation to cancel campaign.
	const cancelCampaignMutation = useMutation({
		mutationKey: ["cancelCampaign"],
		mutationFn: (campaignUID: string) => authenticateAndPostData("/campaigns/cancel-campaign/", {
			campaign_uid: campaignUID,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: (response) => {
			let uid: string = response.data["campaign_uid"]
			campaigns.map(campaign => {
				if (campaign.uid === uid) campaign.status = "cancelled";
				return campaign;
			});
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	// Mutation to archive campaign.
	const archiveCampaignMutation = useMutation({
		mutationKey: ["archiveCampaign"],
		mutationFn: (campaignUID: string) => authenticateAndPostData("/campaigns/archive-campaign/", {
			campaign_uid: campaignUID,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			pageDataQuery.refetch().then();
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	function campaignStatusChipColor(status: string) {
		switch (status) {
			case "creating":
				return "warning";

			case "created":
				return "default";

			case "scheduled":
				return "primary";

			case "running":
				return "primary";

			case "complete":
				return "success";

			case "cancelled":
				return "error";

			case "paused":
				return "warning"

			default:
				return "default";
		}
	}

	function campaignCancelDisabled(campaignStatus: string) {
		return !["created", "running", "paused"].includes(campaignStatus)
	}

	function timestampFormatter(params: ValueFormatterParams, timezone: string) {
		if (!params.value) return "";

		return formatInTimeZone(
			new Date(params.value),
			timezone,
			"hh:mm a, do MMM yyyy"
		).replace(/\b(AM|PM)\b/, (match) => match.toLowerCase());
	}

	const CampaignLink = (params: CustomCellRendererProps) => {
		if (params.data) {
			if (params.data["status"] !== "creating") {
				return (
					<Link style={{color: theme.palette.text.primary}}
								to={urls["campaignEmailDetails"].replace(":campaignUID", params.data["uid"])}>
						{params.value}
					</Link>
				)

			} else {
				return (
					<Typography>
						{params.value}
					</Typography>
				)
			}

		} else {
			<Typography>{params.value}</Typography>
		}
	}

	const StatusBadge = (params: CustomCellRendererProps) => {
		return (
			<Chip size={"small"} color={campaignStatusChipColor(params.value)} label={params.value}/>
		)
	}

	const TableTopRow = (props: {
		value: StatusFilter,
		onChange: (event: React.MouseEvent<HTMLElement>, newValue: string | null) => void,
	}) => {
		return (
			<Box display={"flex"} flexDirection={"row"} justifyContent={"space-between"} sx={{width: "100%", pl: 4}}>
				<ToggleButtonGroup
					color={"primary"}
					value={props.value}
					exclusive
					onChange={props.onChange}
				>
					<ToggleButton size={"small"} value="all">
						All
					</ToggleButton>
					<ToggleButton size={"small"} value="ongoing">
						Ongoing
					</ToggleButton>
					<ToggleButton size={"small"} value="completed">
						Completed
					</ToggleButton>
					<ToggleButton size={"small"} value="cancelled">
						Cancelled
					</ToggleButton>
					<ToggleButton size={"small"} value="archived">
						Archived
					</ToggleButton>
				</ToggleButtonGroup>

				<Button component={Link}
								to={urls["createCampaign"]}
								startIcon={<Add/>}
								variant="contained"
								size={"small"}
								color="primary">
					Create New Campaign
				</Button>
			</Box>
		)
	}

	const ActionsButton = (props: CustomCellRendererProps) => {
		const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
		const open = Boolean(anchorEl);

		const handleClick = (event: any) => {
			setAnchorEl(event.currentTarget);
		};

		const handleClose = () => {
			setAnchorEl(null);
		};

		const handleDuplicate = () => {
			setDuplicateTargetCampUID(props.data["uid"]);
			setOpenDuplicateModal(true);
			handleClose();
		};

		const handleArchive = async () => {
			const archiveConfirmed = await dialogs.confirm(
				`Archiving will remove it from the campaigns table without actually deleting it. This action cannot be undone.`,
				{
					title: "Archive this campaign?",
					cancelText: "No",
					okText: "Yes",
				}
			);
			if (archiveConfirmed) {
				archiveCampaignMutation.mutate(props.data["uid"]);
			}
		}

		const handleDelete = async () => {
			const cancelConfirmed = await dialogs.confirm(
				`Are you sure you want to cancel campaign "${props.data['name']}"? All 
				remaining schedules will be deleted but you can still access campaign data.`,
				{
					title: "Cancel Campaign",
					cancelText: "Cancel",
					okText: "Confirm",
				}
			);
			if (cancelConfirmed) {
				cancelCampaignMutation.mutate(props.data["uid"]);
			}
			handleClose();
		};

		return (
			<>
				<IconButton
					aria-label="more"
					aria-controls={open ? 'long-menu' : undefined}
					aria-expanded={open ? 'true' : undefined}
					aria-haspopup="true"
					onClick={handleClick}
				>
					<MoreVert/>
				</IconButton>
				<Menu
					id="long-menu"
					MenuListProps={{
						'aria-labelledby': 'long-button',
					}}
					anchorEl={anchorEl}
					open={open}
					onClose={handleClose}
				>
					<MenuItem onClick={handleDuplicate} disabled={!["complete", "cancelled"].includes(props.data["status"])}>
						<ContentCopy color={"primary"} sx={{mr: 1}}/>
						<Typography variant="subtitle2">Duplicate</Typography>
					</MenuItem>
					{statusFilter !== "archived" && <MenuItem onClick={handleArchive} disabled={!["complete", "cancelled"].includes(props.data["status"])}>
						<Archive color={"warning"} sx={{mr: 1}}/>
						<Typography variant="subtitle2">Archive</Typography>
					</MenuItem>}
					{statusFilter !== "archived" && <MenuItem onClick={handleDelete} disabled={campaignCancelDisabled(props.data["status"])}>
						<Cancel color={"error"} sx={{mr: 1}}/>
						<Typography variant="subtitle2">Cancel Campaign</Typography>
					</MenuItem>}
				</Menu>
			</>
		);
	}


	// Columns for campaigns table.
	const columnDefs: ColDef[] = [
		{field: "name", headerName: "Campaign Name", cellRenderer: CampaignLink, flex: 2},
		{field: "uid", headerName: "Campaign UID"},
		{field: "created_on_ts", headerName: "Created On", valueFormatter: (params) => timestampFormatter(params, timezone), flex: 2},
		{field: "total_emails_sent", headerName: "Emails Sent"},
		{field: "total_replies_received", headerName: "Replies Received"},
		{field: "contacts_count", headerName: "Contacts"},
		{field: "domains", headerName: "Domains", flex: 2},
		{field: "status", headerName: "Status", cellRenderer: StatusBadge},
		{headerName: "Actions", cellRenderer: ActionsButton, sortable: false, resizable: false},
	]

	function handleStatusFilterChange(_event: React.MouseEvent<HTMLElement>, newValue: string | null) {
		if (newValue) {
			setStatusFilter(newValue as StatusFilter);
		}
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isFetching) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else {
		return (
			<Stack direction={"column"} spacing={4} sx={{pb: 4, height: "100%"}}>
				<Stack direction={"column"} spacing={1}>
					<Typography variant={"h4"} align={"center"} color={"primary"}>
						Launch & Manage Campaigns
					</Typography>
				</Stack>
				<Box sx={{height: "100%"}}>
					<ServerSideDataTable columns={columnDefs}
															 rows={filteredCampaigns}
															 actions={<TableTopRow value={statusFilter} onChange={handleStatusFilterChange}/>}
															 noRowsText={"Add a domain before creating a campaign"}/>
				</Box>

				{/* Duplicate campaign modal */}
				<DulicateCampaignModal open={openDuplicateModal}
															 onClose={() => {
																 setDuplicateTargetCampUID("");
																 setOpenDuplicateModal(false);
															 }}
															 onConfirm={(campName: string) => {
																 duplicateCampaignMutation.mutate({
																	 name: campName, targetCampaignUID: duplicateTargetCampUID
																 });
															 }}/>
			</Stack>
		)
	}
}

function DulicateCampaignModal(props: {
	open: boolean,
	onClose: () => void,
	onConfirm: (campName: string) => void
}) {
	const [inputValue, setInputValue] = useState('');

	const handleConfirm = () => {
		props.onConfirm(inputValue); // Pass the input value to the confirmation handler
		props.onClose(); // Close the dialog
	};

	return (
		<Dialog open={props.open} onClose={props.onClose}>
			<DialogTitle>Duplicate Email Campaign</DialogTitle>
			<DialogContent>
				<Typography variant={"body2"}>
					Duplicating this campaign will copy over all the contacts and messages sequences into a new campaign.
				</Typography>
				<TextField
					label="New Campaign Name"
					type="text"
					fullWidth
					value={inputValue}
					sx={{mt: 2}}
					onChange={(e) => setInputValue(e.target.value)}
				/>
			</DialogContent>
			<DialogActions>
				<Button onClick={props.onClose}>Cancel</Button>
				<Button onClick={handleConfirm} color="primary">
					Create
				</Button>
			</DialogActions>
		</Dialog>
	);
}
