import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import {Box, Divider, Stack, Typography} from "@mui/material";
import {useEffect, useState} from "react";
import {useQuery} from "@tanstack/react-query";
import {format} from "date-fns";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";

interface PageData {
	id: string
	title: string,
	description: string,
	created_at: string,
}

export default function Updates() {
	const [pageData, setPageData] = useState<PageData[]>([]);

	const pageDataQuery = useQuery({
		queryKey: ["UpdatePostsPageData"],
		queryFn: () => authenticateAndFetchData("/update/get-update-posts/"),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	})

	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data.data);
		}
	}, [pageDataQuery.data]);

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={4} sx={{pb: 4, height: "100%"}}>
				<Stack direction={"column"} spacing={1}>
					<Typography variant={"h4"} align={"center"} color={"primary"}>
						Updates
					</Typography>
				</Stack>

				{pageData.length === 0 ? (
					<Typography variant="body1" color="text.secondary" sx={{mt: 4}}>
						🚫 No Updates Available
					</Typography>
				) : (
					pageData.map((update, index) => (
						<Box key={update.id}>
							{/* Title */}
							<Typography variant="h6" fontWeight="bold" gutterBottom>
								{update.title}
							</Typography>

							{/* Date */}
							<Typography variant="caption" color="text.secondary" display="block" gutterBottom>
								{format(new Date(update.created_at), "dd MMM yy HH:mm")}
							</Typography>

							{/* Description */}
							<Stack spacing={1} mt={1}>
								{update.description.split("\n").map((line, idx) => (
									<Typography key={idx} variant="body1">
										{line}
									</Typography>
								))}
							</Stack>

							<Divider sx={{mt: 5}}/>
						</Box>
					))
				)}

			</Stack>
		)
	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
