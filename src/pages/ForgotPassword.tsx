import {<PERSON>, <PERSON><PERSON>, Con<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typo<PERSON>, useTheme} from "@mui/material";
import {Link} from "react-router-dom";
import {urls} from "@routes";
import {useState} from "react";
import {useMutation} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndPostData, retryFn} from "@lib/apis";
import {useSnackbar} from "notistack";
import white_logo from "@assets/branding/coldemailer_white_full_logo.svg";
import black_logo from "@assets/branding/coldemailer_black_full_logo.svg";


function BrandingLogo() {
	const isDarkTheme = useTheme().palette.mode === 'dark'

	if (isDarkTheme) {
		return (
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"}>
				<img src={white_logo} alt={"logo"} style={{width: "18em", height: "auto"}}/>
			</Box>
		)
	} else {
		return (
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"}>
				<img src={black_logo} alt={"logo"} style={{width: "18em", height: "auto"}}/>
			</Box>
		)
	}
}


export default function ForgotPassword() {
	const {enqueueSnackbar} = useSnackbar();

	const [email, setEmail] = useState<string>("");

	const sendResetLinkMutation = useMutation({
		mutationKey: ['sendResetLink', email],
		mutationFn: () => authenticateAndPostData(
			"/auth/reset-password/send-link/",
			{"email_id": email}
		),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			enqueueSnackbar(
				"Success! You will get the password reset link via email if an account with this email id exists.",
				{
					variant: "success",
				});
			setEmail("");
		},
		onError: (error: ApiRequestFailed) => {
			// Show error message.
			console.error(error)
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	function handleForgotPassword() {
		sendResetLinkMutation.mutate();
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<Container maxWidth={"xs"}>
			<BrandingLogo/>
			<Stack direction="column" spacing={1} sx={{mt: 6}}>
				<Typography variant="h3" align={"center"}>Reset Password</Typography>
				<Typography variant="h5" align={"center"}>Please enter your account email id.</Typography>
			</Stack>
			<Stack direction="column" spacing={2} marginTop={5}>
				<TextField type="email"
									 id="email"
									 label="Email ID"
									 variant="outlined"
									 value={email}
									 onChange={e => setEmail(e.target.value)}
									 required/>
			</Stack>
			<Stack direction="column" spacing={2} marginTop={5}>
				<Button variant="contained" color="primary" type="submit" onClick={handleForgotPassword}
								disabled={sendResetLinkMutation.isPending || email === ""}>
					Send Reset Link
				</Button>
				<Typography variant="body2" align={"center"}>
					Go back to <Link to={urls["signup"]}>Log In</Link>.
				</Typography>
			</Stack>
		</Container>
	)
}
