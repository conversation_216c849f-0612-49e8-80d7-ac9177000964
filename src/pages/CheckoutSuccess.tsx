import {Link as ReactRouter<PERSON>ink, useSearchParams} from "react-router-dom";
import {
	<PERSON>ert,
	Avatar,
	Box,
	Button,
	Card,
	CardContent,
	Container,
	Grid2,
	Link,
	List,
	ListItem,
	ListItemIcon,
	ListItemText,
	Paper,
	Stack,
	Typography,
	useTheme,
} from "@mui/material";
import {ArrowRight, Checklist, CreditCard, Download, TaskAlt} from "@mui/icons-material";
import {CalendarIcon} from "@mui/x-date-pickers";
import {useEffect, useState} from "react";
import {useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {urls} from "@routes";
import {PopupModal} from "react-calendly";

interface PageData {
	status_code: string
	status_text: string

	plan_name: string
	billing_period: string
	amount_total: number
	currency: string
	// invoice_pdf_link: string
	credits: number
	// next_billing_ts: number
	feature_list: string[]
}


export default function CheckoutSuccess() {
	const [searchParams] = useSearchParams();
	const theme = useTheme();

	const [pageData, setPageData] = useState<PageData>();
	const [openCalendy, setOpenCalendy] = useState(false);

	// Fetch the page data
	const pageDataQuery = useQuery({
		queryKey: ["dashboard"],
		queryFn: () => authenticateAndFetchData(`/checkout-success/?session_id=${searchParams.get("session_id")}`),
		refetchOnWindowFocus: false,
		retry: retryFn,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data as PageData);
		}
	}, [pageDataQuery.data]);

	function getAmountText(value: number, currency: string): string {
		console.log(currency)
		if (currency === "inr") {
			return "₹" + (value / 100).toString();
		} else {
			return "$" + (value / 100).toString();
		}
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Container sx={{pb: 6}}>
				<Stack direction={"column"} spacing={4} alignItems={"center"}>
					{/* Heading	*/}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"h4"} fontWeight={"bold"} align={"center"}>
							Your Payment is Successful!
						</Typography>
						<Typography variant={"body1"} align={"center"}>
							Your {pageData.plan_name} plan is now active and ready to use.
						</Typography>
					</Stack>

					{/* Plan Details Cards */}
					<Grid2 container spacing={3} sx={{width: "100%"}}>
						<Grid2 size={{xs: 12, sm: 12, md: 6}}>
							<Card variant={"elevation"} sx={{height: "100%"}}>
								<CardContent>
									<Stack direction={"row"} spacing={2} alignItems={"center"}>
										<CreditCard color={"primary"}/>
										<Typography variant={"h5"} fontWeight={"bold"}>
											Plan Details
										</Typography>
									</Stack>
									<List sx={{mt: 2}}>
										<ListItem sx={{px: 0}}>
											<ListItemText primary={"Plan:"}
																		primaryTypographyProps={{align: "left"}}/>
											<ListItemText primary={`${pageData.plan_name} Plan`}
																		primaryTypographyProps={{align: "right", fontWeight: "bold"}}/>
										</ListItem>
										<ListItem sx={{px: 0}}>
											<ListItemText primary={"Billing:"}
																		primaryTypographyProps={{align: "left"}}/>
											<ListItemText primary={pageData.billing_period}
																		primaryTypographyProps={{align: "right", fontWeight: "bold"}}/>
										</ListItem>
										<ListItem sx={{px: 0}}>
											<ListItemText primary={"Amount:"}
																		primaryTypographyProps={{align: "left"}}/>
											<ListItemText primary={getAmountText(pageData.amount_total, pageData.currency)}
																		primaryTypographyProps={{align: "right", fontWeight: "bold", color: "success"}}/>
										</ListItem>
										<ListItem sx={{px: 0}}>
											<ListItemText primary={"Email Credits:"}
																		primaryTypographyProps={{align: "left"}}/>
											<ListItemText primary={pageData.credits}
																		primaryTypographyProps={{align: "right", fontWeight: "bold"}}/>
										</ListItem>
										{/*<ListItem sx={{px: 0}}>*/}
										{/*	<ListItemText primary={"Next Billing:"}*/}
										{/*								primaryTypographyProps={{align: "left"}}/>*/}
										{/*	<ListItemText primary={formattedDateTime(new Date(pageData.next_billing_ts), true)}*/}
										{/*								primaryTypographyProps={{align: "right", fontWeight: "bold"}}/>*/}
										{/*</ListItem>*/}
									</List>
								</CardContent>
							</Card>
						</Grid2>
						<Grid2 size={{xs: 12, sm: 12, md: 6}}>
							<Card variant={"elevation"} sx={{height: "100%"}}>
								<CardContent>
									<Stack direction={"row"} spacing={2} alignItems={"center"}>
										<Checklist color={"primary"}/>
										<Typography variant={"h5"} fontWeight={"bold"}>
											What's Included
										</Typography>
									</Stack>
									<List sx={{
										mt: 2,
										maxHeight: "250px",
										overflowY: "scroll",
										'& .MuiListItemIcon-root': {
											minWidth: "36px"
										}
									}}>
										{pageData.feature_list.map((feature, index) => (
											<ListItem sx={{px: 0}} key={index}>
												<ListItemIcon>
													<TaskAlt color={"success"}/>
												</ListItemIcon>
												<ListItemText primary={feature}/>
											</ListItem>
										))}
									</List>
								</CardContent>
							</Card>
						</Grid2>
					</Grid2>

					{/* Action Buttons */}
					<Box display={"flex"} flexDirection={"row"} justifyContent={"center"} alignItems={"center"} flexWrap={"wrap"}>
						<Button component={ReactRouterLink}
										to={urls["dashboard"]}
										variant={"contained"}
										color={"primary"}
										sx={{m: 1}}
										endIcon={<ArrowRight/>}>
							Go To Dashboard
						</Button>
						{/*<Button component={Link}*/}
						{/*				href={pageData.invoice_pdf_link}*/}
						{/*				target={"_blank"}*/}
						{/*				rel={"noreferrer"}*/}
						{/*				variant={"outlined"}*/}
						{/*				color={"primary"}*/}
						{/*				sx={{m: 1}}*/}
						{/*				startIcon={<Download/>}>*/}
						{/*	Download Invoice*/}
						{/*</Button>*/}
						<Button variant={"outlined"}
										color={"primary"}
										sx={{m: 1}}
										startIcon={<CalendarIcon/>}
										onClick={() => setOpenCalendy(true)}>
							Schedule Onboarding Call
						</Button>
					</Box>

					{/* Help */}
					<Alert severity={"info"} sx={{width: "100%", justifyContent: "center", alignItems: "center"}}>
						Need help getting
						started?&nbsp;
						{/* eslint-disable-next-line no-script-url,jsx-a11y/anchor-is-valid */}
						<a href="javascript:void(Tawk_API.toggle())">Contact our support team</a> or&nbsp;
						{/* eslint-disable-next-line no-script-url,jsx-a11y/anchor-is-valid */}
						<a href="https://youtu.be/Zt7hjRC0AMY?si=RtFz9eCh5Qls2-o4" target={"_blank"} rel={"noreferrer"}>
							watch our getting started video
						</a>
					</Alert>
				</Stack>

				{/* Calendy Modal */}
				<PopupModal
					url="https://calendly.com/junaidansari?hide_gdpr_banner=1&primary_color=ffffff"
					onModalClose={() => setOpenCalendy(false)}
					open={openCalendy}
					/*
					 * react-calendly uses React's Portal feature (https://reactjs.org/docs/portals.html) to render the popup modal. As a result, you'll need to
					 * specify the rootElement property to ensure that the modal is inserted into the correct domNode.
					 */
					rootElement={document.getElementById("root")!}
				/>
			</Container>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
