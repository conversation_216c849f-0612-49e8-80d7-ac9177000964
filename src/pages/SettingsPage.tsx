import {
	<PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	Card<PERSON>ontent,
	Dialog,
	<PERSON>alog<PERSON><PERSON>,
	<PERSON>alogContent,
	DialogContentText,
	DialogTitle,
	Divider,
	Stack,
	TextField,
	Typography,
	FormControlLabel,
	FormGroup,
	styled,
	Switch,
	SwitchProps,
	Chip,
	MenuItem,
	Autocomplete,
} from "@mui/material";
import {useEffect, useState} from "react";
import {Save, Warning} from "@mui/icons-material";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import {useSnackbar} from "notistack";
import {useNavigate} from "react-router-dom";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {urls} from "@routes";

interface PageData {
	status_code: string
	status_text: string

	username: string
	user_email: string
	workspace_name: string
	workspace_id: number,
	blacklist_email: boolean
	user_verified: boolean
	time_zone: string
}

interface CustomizedSwitchProps {
    blackListEmail: boolean;
    handleSaveGreyListEmail: (value: boolean) => void;
}

export default function SettingsPage() {
	const {enqueueSnackbar} = useSnackbar();
	const navigate = useNavigate();

	const [username, setUsername] = useState("");
	const [accountEmail, setAccountEmail] = useState("");
	const [blackListEmail, setBlackListEmail] = useState(false)
	const [userVerified, setUserVerified] = useState(false)
	const [selectedTimezone, setSelectedTimezone] = useState("");


	const [
		workspaceName,
		setWorkspaceName
	] = useState("");
	const [
		workspaceID,
		setWorkspaceID
	] = useState<number | null>(null);
	const timeZones = (Intl as any).supportedValuesOf
	? (Intl as any).supportedValuesOf("timeZone")
	: []; 

	// Fetch the page data
	const pageDataQuery = useQuery({
		queryKey: ["dashboard"],
		queryFn: () => authenticateAndFetchData("/settings/"),
		refetchOnWindowFocus: false,
		retry: retryFn,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			let data: PageData = pageDataQuery.data.data as PageData;
			setUsername(data.username);
			setAccountEmail(data.user_email);
			setWorkspaceName(data.workspace_name);
			setWorkspaceID(data.workspace_id);
			setBlackListEmail(data.blacklist_email)
			setUserVerified(data.user_verified)
			setSelectedTimezone(data.time_zone);
		}
	}, [pageDataQuery.data]);	

	// Mutation for saving workspace settings.
	const saveWorkspaceSettingsMutation = useMutation({
		mutationKey: ["saveWorkspaceSettings"],
		mutationFn: async () => {
				return authenticateAndPostData("/settings/save-workspace/", {
					workspace_name: workspaceName,
					blacklist_email: blackListEmail,
					time_zone: selectedTimezone,
				});
			},
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			// refresh to fetch updated workspace.
			enqueueSnackbar('Successfully Saved Workspace Details!', {
				variant: "success",
			});
			pageDataQuery.refetch(); 
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	const resendVerificationMutation = useMutation({
		mutationKey: ["resendVerification"],
		mutationFn: async () => {
			return authenticateAndPostData("/resend-verification-email/", {});
		},
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			enqueueSnackbar('Verification email has been resent successfully.', {
				variant: "success",
			});
			pageDataQuery.refetch(); 
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data?.message || "Failed to resend verification email", {
				variant: "error",
			});
		}
	});
	
	const handleResendVerficationEmail = () => {
		resendVerificationMutation.mutate();
	};

	const handleSaveGreyListEmail = (value: boolean) => {
		setBlackListEmail(value);
		saveWorkspaceSettingsMutation.mutate();
	};

	const handleSaveTimeZone = (value: string) => {
		setSelectedTimezone(value);		
		saveWorkspaceSettingsMutation.mutate();
	};

	function CustomizedSwitch({ blackListEmail, handleSaveGreyListEmail }: CustomizedSwitchProps) {
        const IOSSwitch = styled((props: SwitchProps) => (
            <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
        ))(({ theme }) => ({
            width: 42,
            height: 26,
            padding: 0,
            '& .MuiSwitch-switchBase': {
                padding: 0,
                margin: 2,
                transitionDuration: '300ms',
                '&.Mui-checked': {
                    transform: 'translateX(16px)',
                    color: '#fff',
                    '& + .MuiSwitch-track': {
                        backgroundColor: '#0288d1',
                        opacity: 1,
                        border: 0,
                        ...theme.applyStyles('dark', {
                            backgroundColor: '#0288d1',
                        }),
                    },
                    '&.Mui-disabled + .MuiSwitch-track': {
                        opacity: 0.5,
                    },
                },
                '&.Mui-focusVisible .MuiSwitch-thumb': {
                    color: '#0288d1',
                    border: '6px solid #fff',
                },
                '&.Mui-disabled .MuiSwitch-thumb': {
                    color: theme.palette.grey[100],
                    ...theme.applyStyles('dark', {
                        color: theme.palette.grey[600],
                    }),
                },
                '&.Mui-disabled + .MuiSwitch-track': {
                    opacity: 0.7,
                    ...theme.applyStyles('dark', {
                        opacity: 0.3,
                    }),
                },
            },
            '& .MuiSwitch-thumb': {
                boxSizing: 'border-box',
                width: 22,
                height: 22,
            },
            '& .MuiSwitch-track': {
                borderRadius: 26 / 2,
                backgroundColor: '#E9E9EA',
                opacity: 1,
                transition: theme.transitions.create(['background-color'], {
                    duration: 500,
                }),
                ...theme.applyStyles('dark', {
                    backgroundColor: '#39393D',
                }),
            },
        }));

        return (
            <FormGroup sx={{ display: "flex", alignItems: "center", justifyContent: "center", width: '78px' }}>
                <FormControlLabel
                    control={<IOSSwitch checked={blackListEmail}
                        onChange={(e) => handleSaveGreyListEmail(e.target.checked)} />}
                    label=""
                />
            </FormGroup>
        );
    }

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else {
		return (
			<Stack direction={"column"}>
				<Stack direction={"column"}>
					<Box display={"flex"} flexDirection={"row"} justifyContent={"space-between"}>
						<Typography variant={"h4"}>
							Settings
						</Typography>
						{/* <Button variant={"contained"}
										size={"small"}
										onClick={() => {
											saveWorkspaceSettingsMutation.mutate()
										}}
										disabled={saveWorkspaceSettingsMutation.isPending}
										startIcon={<Save/>}>
							Save Changes
						</Button> */}
					</Box>
					<Divider sx={{mt: 2, mb: 2}}/>
					<Card>
						<CardContent>
							<Stack direction={"column"} spacing={1}>
								<Typography sx={{color: 'text.secondary', fontSize: 14}}>
									Username
								</Typography>
								<TextField variant="outlined"
													 size={"small"}
													 sx={{maxWidth: 500}}
													 disabled={true}
													 aria-readonly={true}
													 value={username}/>
							</Stack>
							<Stack direction={"column"} spacing={1} sx={{mt: 4}}>
								<Typography sx={{color: 'text.secondary', fontSize: 14}}>
									Account Email
								</Typography>
								<TextField variant="outlined"
													 size={"small"}
													 sx={{maxWidth: 500}}
													 disabled={true}
													 aria-readonly={true}
													 value={accountEmail}/>
							</Stack>
							{userVerified ? 
								(<Chip
									label="Email Verified"
									color="success"
									variant="filled"
									size="small"
									sx={{ mt: 1 }}
								/>)
							:(
							<Button
								variant={"contained"}
								color={"success"}								
								sx={{mt: 2}}
								onClick={handleResendVerficationEmail}
								disabled={resendVerificationMutation.isPending || userVerified}
							>
								{resendVerificationMutation.isPending ? "Resending..." : "Resend Verification Email"}
							</Button>	
							)}						
							<Stack direction={"column"} spacing={1} sx={{mt: 4}}>
								<Typography sx={{color: 'text.secondary', fontSize: 14}}>
									<Box component="span" sx={{color: "error.main"}}>*</Box>&nbsp;
									Workspace Name (max. 50 characters)
								</Typography>
								<TextField variant="outlined"
													 size={"small"}
													 value={workspaceName}
													 sx={{maxWidth: 500}}
													 onChange={(e) => setWorkspaceName(e.target.value)}
													 onBlur={() => saveWorkspaceSettingsMutation.mutate()} />
							</Stack>
							<Divider sx={{mt: 4, mb: 4}}/>
								<Box sx={{ display: "flex", flexDirection: "row", alignItems: "left" }}>
									<CustomizedSwitch
										blackListEmail={blackListEmail}
										handleSaveGreyListEmail={handleSaveGreyListEmail}
									/>
									<Typography variant="subtitle1" sx={{ alignSelf: "center", fontWeight: "bold"}}>
										Receive blacklist alert emails
									</Typography>
									</Box>
							<Divider sx={{mt: 4, mb: 4}}/>
								<Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
								<Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
									Date & Time Settings
								</Typography>								
									<Autocomplete
									options={[
										// Add UTC manually at the top
										{
										label: "UTC (+00:00)",
										value: "UTC",
										},
										...timeZones.map((tz) => {
										const now = new Date();
										const offsetFormatter = new Intl.DateTimeFormat("en-US", {
											timeZone: tz,
											timeZoneName: "shortOffset",
										});
										const offsetParts = offsetFormatter.formatToParts(now);
										const rawOffset = offsetParts
											.find((p) => p.type === "timeZoneName")
											?.value.replace("GMT", "");

										return {
											label: `${tz} (${rawOffset} UTC)`,
											value: tz,
										};
										}),
									]}
									value={
										selectedTimezone
										? {
											label:
												selectedTimezone === "UTC"
												? "UTC (+00:00)"
												: `${selectedTimezone} (${new Intl.DateTimeFormat("en-US", {
													timeZone: selectedTimezone,
													timeZoneName: "shortOffset",
													})
													.formatToParts(new Date())
													.find((p) => p.type === "timeZoneName")
													?.value.replace("GMT", "")} UTC)`,
											value: selectedTimezone,
											}
										: null
									}
									onChange={(_, newValue) => {
										if (newValue) {
										setSelectedTimezone(newValue.value);
										handleSaveTimeZone(newValue.value);
										}
									}}
									renderInput={(params) => (
										<TextField {...params} label="Timezone" sx={{ maxWidth: 400 }} />
									)}
									getOptionLabel={(option) => option.label}
									/>							
								</Box>
							<Divider sx={{mt: 4, mb: 4}}/>
							<Box sx={{p: 2, border: "solid 1px", borderRadius: "12px", borderColor: "error.main"}}>
								<Stack direction={"column"} spacing={1}>
									<Typography sx={{color: 'text.secondary', fontSize: 14}}>
										Delete Workspace
									</Typography>
									<DeleteWorkspaceButton workspaceName={workspaceName} workspaceID={workspaceID!}/>
								</Stack>
							</Box>
						</CardContent>
					</Card>
				</Stack>
			</Stack>
		)
	}
}


export function DeleteWorkspaceButton(props: {
	workspaceName: string,
	workspaceID: number,
}) {
	const {enqueueSnackbar} = useSnackbar();

	const [open, setOpen] = useState(false);
	const [preventDeletion, setPreventDeletion] = useState<boolean>(true);
	const [confirmationInputValue, setConfirmationInputValue] = useState("");

	// Mutation to delete workspace from account.
	const deleteWorkspaceMutation = useMutation({
		mutationKey: ["deleteWorkspace"],
		mutationFn: () => authenticateAndPostData("/workspaces/delete/", {
			workspace_id: props.workspaceID,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			// Navigate to dashboard page. This will also refetch the workspace data so user gets moved to the other one.
			window.location.href = urls["dashboard"]
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	useEffect(() => {
		setPreventDeletion(!(confirmationInputValue === props.workspaceName))
	}, [confirmationInputValue, props.workspaceName]);

	const handleClickOpen = () => {
		setOpen(true);
	};

	const handleClose = () => {
		setOpen(false);
	};

	return (
		<>
			<Button variant={"contained"}
							color={"error"}
							sx={{width: "fit-content"}}
							startIcon={<Warning/>}
							size={"small"}
							onClick={handleClickOpen}>
				Delete This Workspace
			</Button>
			<Dialog
				open={open}
				onClose={handleClose}
				aria-labelledby="delete-workspace-alert"
				aria-describedby="delete-workspace-alert-description"
			>
				<DialogTitle id="delete-workspace-alert">
					Confirm deleting workspace <b>"{props.workspaceName}"?</b>
				</DialogTitle>
				<DialogContent>
					<DialogContentText id="alert-dialog-description">
						Deleting a workspace will remove all the content of the workspace like emails, campaigns, domains,
						any ongoing subscriptions. This action cannot be reversed!
						<br/><br/>
						If you with to preceed please enter the name this workspace below:
					</DialogContentText>
					<TextField variant="outlined"
										 value={confirmationInputValue}
										 size={"small"}
										 sx={{width: "100%", mt: 2}}
										 onChange={e => setConfirmationInputValue(e.target.value)} />
				</DialogContent>
				<DialogActions>
					<Button onClick={handleClose} autoFocus>
						Go Back
					</Button>
					<Button variant={"contained"}
									color={"error"}
									disabled={preventDeletion || deleteWorkspaceMutation.isPending}
									onClick={() => {
										deleteWorkspaceMutation.mutate();
									}}>
						Confirm & Proceed
					</Button>
				</DialogActions>
			</Dialog>
		</>
	);
}
