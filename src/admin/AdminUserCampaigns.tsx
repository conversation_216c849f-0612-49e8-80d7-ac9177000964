import {<PERSON>, Chip, Divider, Stack, Typography, useTheme} from "@mui/material";
import ServerSideDataTable from "@components/ServerSideDataTable";
import {useEffect, useState} from "react";
import {ColDef, ValueFormatterParams} from "ag-grid-community";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import {useMutation, useQuery} from "@tanstack/react-query";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import IconButton from "@mui/material/IconButton";
import {ArrowBack, Delete} from "@mui/icons-material";
import {Link, useNavigate, useParams} from "react-router-dom";
import {CustomCellRendererProps} from "ag-grid-react";
import {urls} from "@routes";
import {useDialogs} from "@toolpad/core";
import {useSnackbar} from "notistack";
import { format } from "date-fns";


interface PageData {
	status_code: number
	status_text: string

	user_email: string
	campaigns: Campaign[]
}

interface Campaign {
	uid: string
	workspace: string
	created_on_ts: number
	name: string
	custom_emails_per_day: number
	status: string
	scheduled_start_datetime_ts: number | null
	reply_to_address: string | null
	sending_domains: Array<string>
	bounces: number
	total_replies: number
	total_sent: number
}


export default function AdminUserCampaigns() {
	const {userId} = useParams();
	const {enqueueSnackbar} = useSnackbar();
	const navigate = useNavigate();
	const theme = useTheme();
	const dialog = useDialogs();

	const [pageData, setPageData] = useState<PageData>();

	// Query to fetch all campaigns.
	const adminUserCampaignsQuery = useQuery({
		queryKey: ["adminUserCampaignsQuery", userId],
		queryFn: () => authenticateAndFetchData(`/admin/get-user-campaigns/?user_id=${userId}`),
		gcTime: 0,
		refetchOnWindowFocus: false,
		retry: retryFn,
	});
	useEffect(() => {
		if (adminUserCampaignsQuery.data) {
			setPageData(adminUserCampaignsQuery.data.data as PageData);
		}
	}, [adminUserCampaignsQuery.data]);

	// Mutation to cancel campaign.
	const cancelCampaignMutation = useMutation({
		mutationKey: ["adminCancelCampaign"],
		mutationFn: (uid: string) => authenticateAndPostData("/admin/cancel-campaign/", {
			"uid": uid,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			adminUserCampaignsQuery.refetch().then();
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	})

	const CampaignDetailsLink = (cellProps: CustomCellRendererProps) => {
		return (
			<Link
				to={urls["adminUserCampaignDetails"].replace(":userId", userId || "").replace(":campaignUID", cellProps.data["uid"])}
				style={{color: theme.palette.text.primary}}>
				{cellProps.value}
			</Link>
		)
	}

	function timestampFormatter(params: ValueFormatterParams) {
		if (params.value !== null) {
			return format(new Date(params.value), "do MMM y, HH:mm:ss (xxx)");
		} else {
			return "---"
		}
	}

	function sendingDomainsFormatter(params: ValueFormatterParams) {
		return params.value.join(", ");
	}

	function campaignStatusChipColor(status: string) {
		switch (status) {
			case "creating":
				return "warning";

			case "created":
				return "default";

			case "scheduled":
				return "primary";

			case "running":
				return "primary";

			case "complete":
				return "success";

			case "cancelled":
				return "error";

			default:
				return "default";
		}
	}

	const StatusBadge = (params: CustomCellRendererProps) => {
		return (
			<Chip size={"small"} color={campaignStatusChipColor(params.value)} label={params.value}/>
		)
	}

	const CancelCampaignButton = (params: CustomCellRendererProps) => {
		return (
			<IconButton size={"small"}
									color={"error"}
									disabled={!["created", "running", "paused"].includes(params.data["status"])}
									onClick={async () => {
										const cancelConfirmed = await dialog.confirm(
											`Are you sure you want to cancel campaign ${params.data["name"]}?`,
											{
												title: "Cancel Campaign",
												cancelText: "No",
												okText: "Yes",
											}
										);
										if (cancelConfirmed) {
											cancelCampaignMutation.mutate(params.data["uid"]);
										}
									}}>
				<Delete/>
			</IconButton>
		)
	}

	// Columns for campaigns table.
	const columnDefs: ColDef[] = [
		{field: "name", headerName: "Campaign Name", cellRenderer: CampaignDetailsLink, flex: 2},
		{
			field: "created_on_ts",
			headerName: "Created On",
			valueFormatter: timestampFormatter,
			comparator: (valueA, valueB, nodeA, nodeB) => {
				return nodeA.data["created_on_ts"] > nodeB.data["created_on_ts"] ? 1 : -1;
			},
			flex: 2,
		},
		{field: "workspace", headerName: "Workspace"},
		// {field: "custom_emails_per_day", headerName: "Custom Email Limit"},
		{
			field: "scheduled_start_datetime_ts",
			headerName: "Scheduled Start",
			valueFormatter: timestampFormatter,
			comparator: (valueA, valueB, nodeA, nodeB) => {
				let value1 = nodeA.data["scheduled_start_datetime_ts"] !== null ? nodeA.data["scheduled_start_datetime_ts"] : 0;
				let value2 = nodeB.data["scheduled_start_datetime_ts"] !== null ? nodeB.data["scheduled_start_datetime_ts"] : 0;

				if (value1 === value2) return 0;
				return value1 > value2 ? 1 : -1;
			},
		},
		{field: "total_sent", headerName: "Total Emails Sent"},
		{field: "total_replies", headerName: "Total Replies"},
		{field: "reply_to_address", headerName: "Reply To"},
		{field: "sending_domains", headerName: "Subdomains", valueFormatter: sendingDomainsFormatter},
		{field: "bounces", headerName: "Bounced"},
		{field: "status", headerName: "Status", cellRenderer: StatusBadge},
		{headerName: "Cancel", cellRenderer: CancelCampaignButton, resizable: false},
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (adminUserCampaignsQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (adminUserCampaignsQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={adminUserCampaignsQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={2} sx={{pb: 4, height: "100%"}}>
				<Stack direction={"row"} spacing={2} alignItems={"center"}>
					<IconButton onClick={() => navigate(-1)}><ArrowBack/></IconButton>
					<Typography fontWeight={"bold"} variant={"h5"}>Campaigns ({pageData.user_email})</Typography>
				</Stack>
				<Divider/>
				<Box sx={{height: "100%"}}>
					<ServerSideDataTable columns={columnDefs} rows={pageData.campaigns}/>
				</Box>
			</Stack>
		)
	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
