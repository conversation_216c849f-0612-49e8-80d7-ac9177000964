import {useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import {Card, CardContent, Stack, Typography, Container} from "@mui/material";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {useEffect, useState} from "react";


interface PageData {
    status_code: number
    status_text: string
	total_domains: number
	total_users: number
	paid_users: number
	free_users: number
	warmup_emails_sent_today: number
	campaign_emails_sent_today: number
	total_email_addresses: number
	active_campaigns: number
	total_unsubscribed_emails: number
	total_replies_received: number
	total_signup_user_today: number
	total_domains_blacklisted_today: number,
	total_domains_blacklisted: number,
	total_aws_identities: number,
}

export default function AdminDashboard() {
	const [pageData, setPageData] = useState<PageData>();

	// Query to authenticate and fetch page data.
	const pageDataQuery = useQuery({
		queryKey: ["adminDashboardPageData"],
		queryFn: () => authenticateAndFetchData("/admin/dashboard/"),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data);
		}
	}, [pageDataQuery.data]);

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Container sx={{pb: 6}}>
				<Stack direction={"column"} spacing={6}>
					{/* Stat Cards */}
					<Stack direction={"row"} spacing={2} flexWrap={"wrap"} useFlexGap={true}>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Domains</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_domains}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Users</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_users}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Paid Users</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.paid_users}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Free Users</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.free_users}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Warmup Emails Sent today</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.warmup_emails_sent_today}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Campaign Emails Sent Today</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.campaign_emails_sent_today}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Email Addresses</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_email_addresses}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Active Campaigns</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.active_campaigns}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Unsubscribed Emails</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_unsubscribed_emails}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Replies Received</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_replies_received}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total New Signups today</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_signup_user_today}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Domains Blacklisted Today</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_domains_blacklisted_today}</Typography>
								</Stack>
							</CardContent>
						</Card>
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Domains Blacklisted</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_domains_blacklisted}</Typography>
								</Stack>
							</CardContent>
						</Card>				
						<Card sx={{flexGrow: 1, flexBasis: 0, minWidth: "250px"}}>
							<CardContent>
								<Stack direction={"column"} spacing={1}>
									<Typography variant={"h6"}>Total Identities in use on AWS</Typography>
									<Typography variant={"body1"} sx={{fontSize: "2em"}}>{pageData.total_aws_identities}/10000</Typography>
								</Stack>
							</CardContent>
						</Card>
					</Stack>					
				</Stack>
			</Container>
		)
	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
