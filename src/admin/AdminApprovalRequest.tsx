import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import React, {useEffect, useState} from "react";
import {useMutation, useQuery} from "@tanstack/react-query";
import {useSnackbar} from "notistack";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {Box, Button, Typography} from "@mui/material";
import {ColDef} from "ag-grid-community";
import {CustomCellRendererProps} from "ag-grid-react";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import {styled} from "@mui/material/styles";
import Dialog from "@mui/material/Dialog";
import ServerSideDataTable from "@components/ServerSideDataTable";
import {Check, Close} from "@mui/icons-material";

interface ApprovalRequest {
	uid: string
	subject: string
	body: string
	blocked_reason: string
	user_email: string
}

interface PageData {
	status_code: number
	status_text: string

	pending_requests: ApprovalRequest[]
}

export default function AdminApprovalRequest() {
	const {enqueueSnackbar} = useSnackbar();

	const [pageData, setPageData] = useState<PageData>();

	// Query for fetching salutation words data.
	const pageDataQuery = useQuery({
		queryKey: ["emailContentApprovalRequest"],
		queryFn: () => authenticateAndFetchData("/admin/email-content-approval-request/"),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	})
	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data);
		}
	}, [pageDataQuery.data]);

	// Mutation for approving or rejecting request.
	const approveOrRejectMutation = useMutation({
		mutationKey: ["AdminApproveOrRejectRequest"],
		mutationFn: (data: {message_uid: string, approved: boolean}) => authenticateAndPostData("/admin/email-content-approval-request/", {
			message_uid: data.message_uid,
			approved: data.approved,
		}),
		onSuccess: (response) => {
			let messageUID: string = response.data["message_uid"];
			let approved: boolean = response.data["approved"];
			enqueueSnackbar(`Message ${messageUID} has been ${approved ? "Approved" : "Rejected"}`, {
				variant: "success",
			});
			pageDataQuery.refetch().then();
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	const BodyMessageDialog = (params: CustomCellRendererProps) => {
		const [open, setOpen] = useState(false);

		const handleClickOpen = () => {
			setOpen(true);
		};

		const handleClose = () => {
			setOpen(false);
		};

		const BootstrapDialog = styled(Dialog)(({theme}) => ({
			'& .MuiDialogContent-root': {
				padding: theme.spacing(2),
			},
			'& .MuiDialogActions-root': {
				padding: theme.spacing(1),
			},
		}));

		return (
			<React.Fragment>
				<Button size={"small"} onClick={handleClickOpen}>
					View Body
				</Button>
				<BootstrapDialog
					onClose={handleClose}
					aria-labelledby="view-reply-dialog"
					open={open}
				>
					<DialogContent>
						<Typography gutterBottom>
							<pre style={{whiteSpace: "pre-wrap", wordWrap: "break-word"}}>
								{params.value}
							</pre>
						</Typography>
					</DialogContent>
					<DialogActions>
						<Button autoFocus onClick={handleClose}>
							Close
						</Button>
					</DialogActions>
				</BootstrapDialog>
			</React.Fragment>
		)
	}

	const ApproveButton = (cellProps: CustomCellRendererProps) => {
		return (
			<Button color={"success"} size={"small"} startIcon={<Check/>} onClick={() => {
				approveOrRejectMutation.mutate({message_uid: cellProps.data["uid"], approved: true});
			}}>
				Approve
			</Button>
		)
	}

	const RejectButton = (cellProps: CustomCellRendererProps) => {
		return (
			<Button color={"error"} size={"small"} startIcon={<Close/>} onClick={() => {
				approveOrRejectMutation.mutate({message_uid: cellProps.data["uid"], approved: false});
			}}>
				Reject
			</Button>
		)
	}

	const columnDefs: ColDef[] = [
		{field: "uid", headerName: "Message UID"},
		{field: "user_email", headerName: "Email Address"},		
		{field: "blocked_reason", headerName: "Reason"},
		{field: "subject", headerName: "Subject"},
		{field: "body", headerName: "Body", cellRenderer: BodyMessageDialog, sortable: false},
		{headerName: "Approve", cellRenderer: ApproveButton, sortable: false},
		{headerName: "Reject", cellRenderer: RejectButton, resizable: false, sortable: false},
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Box sx={{height: '100%'}}>
				<ServerSideDataTable columns={columnDefs}
														 rows={pageData.pending_requests}
														 noRowsText={"No pending requests."}/>
			</Box>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
