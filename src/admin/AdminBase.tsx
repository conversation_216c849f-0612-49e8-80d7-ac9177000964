import {Navigation} from "@toolpad/core";
import DashboardIcon from "@mui/icons-material/Dashboard";
import coldEmailerTheme from "@assets/themes/coldEmailerTheme";
import {SnackbarProvider} from "notistack";
import {DashboardLayout} from "@toolpad/core/DashboardLayout";
import {Box, ThemeProvider, useTheme} from "@mui/material";
import {Outlet} from "react-router-dom";
import {AppProvider} from "@toolpad/core/react-router-dom";
import white_logo from "@assets/branding/coldemailer_white_full_logo.svg";
import black_logo from "@assets/branding/coldemailer_black_full_logo.svg";
import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import PageLoading from "@components/PageLoading";
import {useQuery} from "@tanstack/react-query";
import {useEffect, useState} from "react";
import AdminAccessForbidden from "@admin/AdminAccessForbidden";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {CreditCard, FontDownloadOff, Groups, ThumbUp, WavingHand, Update, Campaign} from "@mui/icons-material";


interface PageData {
	status_code: number
	status_text: string
	message: string
}

const NAVIGATION: Navigation = [
	{
		segment: "admin/dashboard",
		title: 'Dashboard',
		icon: <DashboardIcon/>,
	},
	{
		segment: "admin/users",
		title: 'Users',
		icon: <Groups/>,
	},
	// {
	// 	segment: "admin/all-campaign",
	// 	title: 'All Campaign',
	// 	icon: <Campaign/>,
	// },
	{
		segment: "admin/spam-words",
		title: 'Manage Spam Words',
		icon: <FontDownloadOff/>,
	},
	{
		segment: "admin/salutation-words",
		title: 'Manage Salutation Words',
		icon: <WavingHand/>,
	},
	{
		segment: "admin/subscription-plans",
		title: 'Manage Subscription Plans',
		icon: <CreditCard/>,
	},
	{
		segment: "admin/email-content-approval",
		title: 'Content Approvals',
		icon: <ThumbUp/>,
	},
	{
		segment: "admin/update",
		title: 'Updates',
		icon: <Update/>,
	},
];

export default function AdminBase() {
	const [pageData, setPageData] = useState<PageData>();

	// Authenticate user as admin.
	const adminAuthQuery = useQuery({
		queryKey: ["adminBaseAuthQuery"],
		queryFn: () => authenticateAndFetchData("/admin/auth/"),
		gcTime: 0,
		retry: retryFn
	});
	useEffect(() => {
		if (adminAuthQuery.data) {
			setPageData(adminAuthQuery.data.data as PageData);
		}
	}, [adminAuthQuery.data]);

	// ========================================================================
	// --------------------------- MAIN RENDER CODE ---------------------------
	// ========================================================================

	if (adminAuthQuery.isLoading) {
		return (
			<ThemeProvider theme={coldEmailerTheme}>
				<Box sx={{width: "100vw", height: "100vh", boxSizing: "border-box"}}>
					<PageLoading/>
				</Box>
			</ThemeProvider>
		)

	} else if (adminAuthQuery.error as unknown as ApiRequestFailed) {
		let error = adminAuthQuery.error as ApiRequestFailed

		if (error.statusCode === 403) {
			return (
				<ThemeProvider theme={coldEmailerTheme}>
					<Box sx={{width: "100vw", height: "100vh", boxSizing: "border-box"}}>
						<AdminAccessForbidden/>
					</Box>
				</ThemeProvider>
			)

		} else {
			return (
				<ThemeProvider theme={coldEmailerTheme}>
					<Box sx={{w: "100vw", h: "100vh", boxSizing: "border-box"}}>
						<PageDataErrorHandler error={adminAuthQuery.error as unknown as ApiRequestFailed}/>
					</Box>
				</ThemeProvider>
			)
		}

	} else if (pageData) {
		return (
			<AppProvider
				navigation={NAVIGATION}
				theme={coldEmailerTheme}
				branding={{
					logo: <BrandingLogo/>,
					title: "ADMIN",
				}}
			>
				<SnackbarProvider>
					<DashboardLayout>
						<Box sx={{
							p: 3,
							height: "100%"
						}}>
							<Outlet/>
						</Box>
					</DashboardLayout>
				</SnackbarProvider>
			</AppProvider>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}


function BrandingLogo() {
	const isDarkTheme = useTheme().palette.mode === 'dark'

	if (isDarkTheme) {
		return (
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"} sx={{height: "100%"}}>
				<img src={white_logo} alt={"logo"} style={{width: "10em", height: "auto"}}/>
			</Box>
		)
	} else {
		return (
			<Box display={"flex"} justifyContent={"center"} alignItems={"center"} sx={{height: "100%"}}>
				<img src={black_logo} alt={"logo"} style={{width: "10em", height: "auto"}}/>
			</Box>
		)
	}
}
