import {<PERSON>, Chip, Divider, Stack, Typography, useTheme, ToggleButton, ToggleButtonGroup,} from "@mui/material";
import ServerSideDataTable from "@components/ServerSideDataTable";
import {useEffect, useState} from "react";
import {ColDef} from "ag-grid-community";
import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import {useQuery} from "@tanstack/react-query";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {Link, useParams} from "react-router-dom";
import {CustomCellRendererProps} from "ag-grid-react";
import {urls} from "@routes";


interface PageData {
  campaign_id: string | number;
  campaign_name: string;
  user_id: number;
  user_email: string;
  archived: boolean;
  status: string;
}


type StatusFilter = "all" | "ongoing" | "completed" | "cancelled" | "archived";

export default function AdminAllCampaign() {
    const {userId} = useParams();
    const theme = useTheme();    

    const [pageData, setPageData] = useState<PageData[]>([]);
    const [statusFilter, setStatusFilter] = useState<StatusFilter>("ongoing")
    const [filteredCampaigns, setFilteredCampaigns] = useState<PageData[]>([]);

    // Query to fetch all campaigns.
    const adminAllCampaignsQuery = useQuery({
        queryKey: ["adminAllCampaignsQuery", userId],
        queryFn: () => authenticateAndFetchData(`/admin/admin-get-all-campaign/`),
        gcTime: 0,
        refetchOnWindowFocus: false,
        retry: retryFn,
    });
    useEffect(() => {
        if (adminAllCampaignsQuery.data) {
            setPageData(adminAllCampaignsQuery.data.data.data);
        }
    }, [adminAllCampaignsQuery.data]);

    useEffect(() => {
        if (statusFilter === "all") {
            setFilteredCampaigns(pageData.filter(camp => !camp.archived));
        } else if (statusFilter === "ongoing") {
            setFilteredCampaigns(pageData.filter(camp => !camp.archived && ["created", "running", "scheduled"].includes(camp.status)));
        } else if (statusFilter === "completed") {
            setFilteredCampaigns(pageData.filter(camp => !camp.archived && camp.status === "complete"));
        } else if (statusFilter === "cancelled") {
            setFilteredCampaigns(pageData.filter(camp => !camp.archived && camp.status === "cancelled"));
        } else {            
            setFilteredCampaigns(pageData.filter(camp => camp.archived));
        }
    }, [pageData, statusFilter]);

         
    const CampaignDetailsLink = (cellProps: CustomCellRendererProps) => {
            return (
                <Link
                    to={urls["adminUserCampaignDetails"].replace(":userId", cellProps.data["user_id"] || "").replace(":campaignUID", cellProps.data["campaign_id"])}
                    state={{ from: "AllCampaign" }}
                    style={{color: theme.palette.text.primary}}>
                    {cellProps.value}
                </Link>
            )
        }
    
        function campaignStatusChipColor(status: string) {
                switch (status) {
                    case "creating":
                        return "warning";
        
                    case "created":
                        return "default";
        
                    case "scheduled":
                        return "primary";
        
                    case "running":
                        return "primary";
        
                    case "complete":
                        return "success";
        
                    case "cancelled":
                        return "error";
        
                    default:
                        return "default";
                }
            }
        
        const StatusBadge = (params: CustomCellRendererProps) => {
            return (
                <Chip size={"small"} color={campaignStatusChipColor(params.value)} label={params.value}/>
            )
        }
    const TableTopRow = (props: {
            value: StatusFilter,
            onChange: (event: React.MouseEvent<HTMLElement>, newValue: string | null) => void,
        }) => {
            return (
                <Box display={"flex"} flexDirection={"row"} justifyContent={"space-between"} sx={{width: "100%", pl: 4}}>
                    <ToggleButtonGroup
                        color={"primary"}
                        value={props.value}
                        exclusive
                        onChange={props.onChange}
                    >
                        <ToggleButton size={"small"} value="all">
                            All
                        </ToggleButton>
                        <ToggleButton size={"small"} value="ongoing">
                            Ongoing
                        </ToggleButton>
                        <ToggleButton size={"small"} value="completed">
                            Completed
                        </ToggleButton>
                        <ToggleButton size={"small"} value="cancelled">
                            Cancelled
                        </ToggleButton>
                        <ToggleButton size={"small"} value="archived">
                            Archived
                        </ToggleButton>
                    </ToggleButtonGroup>
                </Box>
            )
        }

    const columnDefs: ColDef[] = [
            {field: "campaign_id", headerName: "Campaign ID", cellRenderer: CampaignDetailsLink, flex: 2},
            {field: "campaign_name", headerName: "Campaign Name", cellRenderer: CampaignDetailsLink, flex: 2},
            {field: "user_email",headerName: "User Email"},
            {field: "status", headerName: "Campaign Status", cellRenderer: StatusBadge},           
        ]                           
    
    function handleStatusFilterChange(_event: React.MouseEvent<HTMLElement>, newValue: string | null) {
            if (newValue) {
                setStatusFilter(newValue as StatusFilter);
            }
        }

	if (adminAllCampaignsQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (adminAllCampaignsQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={adminAllCampaignsQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={2} sx={{pb: 4, height: "100%"}}>
				<Stack direction={"row"} spacing={2} alignItems={"center"}>					
					<Typography fontWeight={"bold"} variant={"h5"}>All Campaigns</Typography>
				</Stack>
				<Divider/>
				<Box sx={{height: "100%"}}>
					<ServerSideDataTable columns={columnDefs} rows={filteredCampaigns} actions={<TableTopRow value={statusFilter} onChange={handleStatusFilterChange}/>}/>
				</Box>
			</Stack>
		)
	} else {
		// Ideally it should not reach here.
		return <></>
	}
}