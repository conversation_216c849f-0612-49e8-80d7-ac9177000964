import {<PERSON>, <PERSON><PERSON>, Chip, DialogContentTex<PERSON>, Divider, <PERSON>ack, Typography, Grid2} from "@mui/material";

import IconButton from "@mui/material/IconButton";
import StatCard from "@components/StatCard";
import {Arrow<PERSON><PERSON>, <PERSON><PERSON>hart , ContactMail, Unsubscribe, Send, Message} from "@mui/icons-material";
import {useNavigate, useParams, useLocation} from "react-router-dom";
import * as React from "react";
import {useEffect, useState} from "react";
import {useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {useDialogs} from "@toolpad/core";
import {ColDef, ValueFormatterParams} from "ag-grid-community";
import ServerSideDataTable from "@components/ServerSideDataTable";
import {CustomCellRendererProps} from "ag-grid-react";
import { format } from "date-fns";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import Dialog from "@mui/material/Dialog";
import {styled} from "@mui/material/styles";
import {urls} from "@routes";

interface PageData {
	status_code: number
	status_text: string

	user_email: string

	campaign_name: string
	messages: CampaignMessage[]
	contacts: Contacts[]
	schedules: Schedule[]
	campaign: campaign
}

interface CampaignMessage {
	uid: string
	subject: string
	body: string
	next_message_days: number
	spam_score: number
	verdict: string
	suggestions: Array<string>
}

interface Contacts {
	uid: string
	email_id: string
	attriutes: any
	sending_email: string
}

interface campaign {
  total_contacts: number;
  total_sent: number;
  total_bounced: string;
  total_unsubscribe: number;
  negative_replies: number
  neutral_replies: number 
  positive_replies: number
  total_replies: number
  bounce_rate: number
}

interface Schedule {
	uid: string
	contact: string
	message_uid: string
	schedule_date_ts: string | null
	status: string
	sent_on_ts: string | null
	aws_message_id: string | null
	email_s3_key: string | null
	reply_received: boolean
	reply_classification: string | null
}

export default function AdminUserCampaignDetails() {
	const {userId, campaignUID} = useParams();
	const navigate = useNavigate();
	const dialogs = useDialogs();

	const [pageData, setPageData] = useState<PageData>();
	const location = useLocation();
	const backUrl = location.state?.from === "AllCampaign" ? urls["adminAllCampaign"] : `/admin/users/${userId}/campaigns`;
	
	// Query to fetch user details.
	const pageDataQuery = useQuery({
		queryKey: ["adminCampaignDetails", userId, campaignUID],
		queryFn: () => authenticateAndFetchData(`/admin/get-campaign-details/?user_id=${userId}&campaign_uid=${campaignUID}`),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data);
		}
	}, [pageDataQuery.data]);

	function suggestionsFormatter(params: ValueFormatterParams) {
		return params.value.join("\n");
	}

	const EmailBodyCellRenderer = (props: CustomCellRendererProps) => {
		return (
			<Button variant={"text"} onClick={async () => {
				await dialogs.alert(
					(
						<DialogContentText sx={{whiteSpace: 'pre-line'}}>
							{props.value}
						</DialogContentText>
					),
					{
						title: "Message Body",
					}
				);
			}}>
				View
			</Button>
		)
	}

	function timestampFormatter(params: ValueFormatterParams) {
		if (params.value !== null) {
			return format(new Date(params.value), "do MMM y, HH:mm:ss (xxx)");
		} else {
			return "---"
		}
	}

	const ScheduleStatusCell = (cellProps: CustomCellRendererProps) => {
		switch (cellProps.value) {
			case "created":
				return <Chip label={"Email Created"} size={"small"} color={"default"} variant={"filled"}/>;

			case "sent":
				return <Chip label={"Email Sent"} size={"small"} color={"primary"} variant={"filled"}/>;

			case "failed":
				return <Chip label={"Failed"} size={"small"} color={"error"} variant={"filled"}/>;

			case "replied":
				return <Chip label={"Reply Received"} size={"small"} color={"success"} variant={"filled"}/>;

			case "cancelled_reply_received":
				return <Chip label={"Sequence Complete"} size={"small"} color={"success"} variant={"outlined"}/>;

			case "cancelled_unsubscribed":
				return <Chip label={"Unsubscribed"} size={"small"} color={"warning"} variant={"outlined"}/>;

			case "cancelled_campaign_stopped":
				return <Chip label={"Cancelled"} size={"small"} color={"error"} variant={"outlined"}/>;

			case "cancelled_bad_email":
				return <Chip label={"Bad Email"} size={"small"} color={"error"} variant={"outlined"}/>;

			default:
				return <Chip label={"N/A"} size={"small"} color={"default"} variant={"filled"}/>;
		}
	}
	
	// Columns for email messages table.
	const emailMessagesColumnDefs: ColDef[] = [
		{field: "uid", headerName: "Message UID"},
		{field: "subject", headerName: "Subject"},
		{field: "body", headerName: "Body Message", cellRenderer: EmailBodyCellRenderer},
		{field: "next_message_days", headerName: "Next Message Days"},
		{field: "spam_score", headerName: "Spam Score"},
		{field: "verdict", headerName: "Verdict"},
		{field: "suggestions", headerName: "Suggestions", valueFormatter: suggestionsFormatter, resizable: false},
	]

	// Columns for contacts table.
	const contactsColumnDefs: ColDef[] = [
		{field: "uid", headerName: "Contact UID"},
		{field: "email_id", headerName: "Email Address"},
		{field: "attributes", headerName: "Variables"},
		{field: "sending_email", headerName: "Sending Email", resizable: false},
	]

	// Columns for schedules table.
	const schedulesColumnDefs: ColDef[] = [
		{field: "uid", headerName: "Schedule UID"},
		{field: "contact", headerName: "Contact"},
		{field: "message_uid", headerName: "Message UID"},
		{field: "schedule_date_ts", headerName: "Scheduled For", valueFormatter: timestampFormatter},
		{field: "sent_on_ts", headerName: "Sent On", valueFormatter: timestampFormatter},
		{field: "aws_message_id", headerName: "AWS Msg ID"},
		{field: "email_s3_key", headerName: "Reply S3 Key"},
		{field: "reply_received", headerName: "Reply Received"},
		{field: "reply_classification", headerName: "Reply Class"},
		{field: "status", headerName: "Status", cellRenderer: ScheduleStatusCell},
		{field: "action", headerName: "Action", resizable: false,    
		cellRenderer: (params: any) => {
		if (params.data?.email_s3_key) {
			return (
			<ViewReply
				replyFrom={params.data.contact}
				s3key={params.data.email_s3_key}
			/>
			);
		}
		return null;
		},
	},
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={2} sx={{pb: 4}}>
				<Stack direction={"row"} spacing={2} alignItems={"center"}>
					<IconButton onClick={() => navigate(backUrl)}><ArrowBack/></IconButton>
					<Typography fontWeight={"bold"} variant={"h5"}>
						Campaign {pageData.campaign_name} ({pageData.user_email})
					</Typography>
				</Stack>
				<Divider/>
				<Stack direction={"column"} spacing={4}>
					<Grid2 container spacing={3}>
						<Grid2 size={{xs: 12, sm: 6, md: 3}}>
							<StatCard
								icon={<BarChart />}
								heading="Total Bounced Display"
								value={`${pageData.campaign.total_bounced} (${pageData.campaign.bounce_rate})%`}
								color="#3f51b5"
							/>
						</Grid2>
						<Grid2 size={{xs: 12, sm: 6, md: 3}}>
							<StatCard
								icon={<ContactMail/>}
								heading="Total Contacts"
								value={pageData.campaign.total_contacts}
								color="#2196f3"
							/>
						</Grid2>
						<Grid2 size={{xs: 12, sm: 6, md: 3}}>
							<StatCard
								icon={<Message/>}
								heading="Total Replies (POS|NEG|NEU)"
								value={`${pageData.campaign.total_replies} (${pageData.campaign.positive_replies} | ${pageData.campaign.negative_replies} | ${pageData.campaign.neutral_replies})`}
								color="#009688"
							/>
						</Grid2>
						<Grid2 size={{xs: 12, sm: 6, md: 3}}>
							<StatCard
								icon={<Send/>}
								heading="Total Sent"
								value={pageData.campaign.total_sent}
								color="#673ab7"
							/>
						</Grid2>
						<Grid2 size={{xs: 12, sm: 6, md: 3}}>
							<StatCard
								icon={<Unsubscribe/>}
								heading="Total Unsubscribe"
								value={pageData.campaign.total_unsubscribe ?? 0}
								color="#ff5722"
							/>
						</Grid2>					
					</Grid2>
					{/* -------------- EMAIL MESSAGES -------------- */}
					<Stack direction={"column"} spacing={2}>
						<Typography variant={"h6"} fontWeight={"bold"}>Email Messages:</Typography>
						<Box sx={{height: "350px"}}>
							<ServerSideDataTable columns={emailMessagesColumnDefs} rows={pageData.messages}/>
						</Box>
					</Stack>
					{/* -------------- CONTACTS -------------- */}
					<Stack direction={"column"} spacing={2}>
						<Typography variant={"h6"} fontWeight={"bold"}>Contacts:</Typography>
						<Box sx={{height: "500px"}}>
							<ServerSideDataTable columns={contactsColumnDefs} rows={pageData.contacts}/>
						</Box>
					</Stack>
					{/* -------------- SCHEDULES -------------- */}
					<Stack direction={"column"} spacing={2}>
						<Typography variant={"h6"} fontWeight={"bold"}>Schedules:</Typography>
						<Box sx={{height: "500px"}}>
							<ServerSideDataTable columns={schedulesColumnDefs} rows={pageData.schedules}/>
						</Box>
					</Stack>
				</Stack>
			</Stack>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}

const BootstrapDialog = styled(Dialog)(({theme}) => ({
	'& .MuiDialogContent-root': {
		padding: theme.spacing(2),
	},
	'& .MuiDialogActions-root': {
		padding: theme.spacing(1),
	},
}));

function ViewReply(props: {
	replyFrom: string,
	s3key: string,
}) {
	const [open, setOpen] = React.useState(false);
	const [replyMessage, setReplyMessage] = React.useState("");

	// Query to fetch reply email message.
	const replyMessageQuery = useQuery({
		queryKey: ["replyMessageQuery", props.replyFrom, props.s3key],
		queryFn: () => authenticateAndFetchData(`/campaigns/get-reply-email-message/?key=${props.s3key}`),
		gcTime: 5000,
		retry: retryFn,
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (replyMessageQuery.data) {
			setReplyMessage(replyMessageQuery.data.data["message"]);
		}
	}, [replyMessageQuery.data]);

	const handleClickOpen = () => {
		setOpen(true);
	};
	const handleClose = () => {
		setOpen(false);
	};

	if (replyMessageQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else {
		return (
			<React.Fragment>
				<Button onClick={handleClickOpen} sx={{ marginLeft: "-2px" }} >
					View Reply
				</Button>
				<BootstrapDialog
					onClose={handleClose}
					aria-labelledby="view-reply-dialog"
					open={open}
				>
					<DialogTitle sx={{m: 0, p: 2}} id="view-reply-dialog">
						Reply from <b>{props.replyFrom}</b>
					</DialogTitle>
					<DialogContent dividers>
						<Typography gutterBottom>
							<pre style={{whiteSpace: "pre-wrap", wordWrap: "break-word"}}>{replyMessage}</pre>
						</Typography>
					</DialogContent>
					<DialogActions>
						<Button autoFocus onClick={handleClose}>
							Close
						</Button>
					</DialogActions>
				</BootstrapDialog>
			</React.Fragment>
		);
	}
}
