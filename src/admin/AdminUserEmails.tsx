import {Box, Divider, Stack, Typography} from "@mui/material";
import ServerSideDataTable from "@components/ServerSideDataTable";
import {useEffect, useState} from "react";
import {ColDef} from "ag-grid-community";
import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import {useQuery} from "@tanstack/react-query";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import IconButton from "@mui/material/IconButton";
import {ArrowBack} from "@mui/icons-material";
import {useNavigate, useParams} from "react-router-dom";


interface PageData {
	status_code: number
	status_text: string

	user_email: string
	emails: Email[]
}

interface Email {
	id: number
	address: string
	username: string
	subdomain: string
}


export default function AdminUserEmails() {
	const {userId} = useParams();
	const navigate = useNavigate();

	const [pageData, setPageData] = useState<PageData>();

	// Query to fetch all emails.
	const adminUserEmailsQuery = useQuery({
		queryKey: ["adminUserEmailsQuery", userId],
		queryFn: () => authenticateAndFetchData(`/admin/get-user-emails/?user_id=${userId}`),
		gcTime: 0,
		refetchOnWindowFocus: false,
		retry: retryFn,
	});
	useEffect(() => {
		if (adminUserEmailsQuery.data) {
			setPageData(adminUserEmailsQuery.data.data as PageData);
		}
	}, [adminUserEmailsQuery.data]);

	// Columns for emails table.
	const columnDefs: ColDef[] = [
		{field: "address", headerName: "Email ID"},
		{field: "username", headerName: "Username"},
		{field: "subdomain", headerName: "Subdomain", resizable: false},
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (adminUserEmailsQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (adminUserEmailsQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={adminUserEmailsQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={2} sx={{pb: 4, height: "100%"}}>
				<Stack direction={"row"} spacing={2} alignItems={"center"}>
					<IconButton onClick={() => navigate(-1)}><ArrowBack/></IconButton>
					<Typography fontWeight={"bold"} variant={"h5"}>Emails ({pageData.user_email})</Typography>
				</Stack>
				<Divider/>
				<Box sx={{height: "100%"}}>
					<ServerSideDataTable columns={columnDefs} rows={pageData.emails}/>
				</Box>
			</Stack>
		)
	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
