import {
	Box,
	Button,
	Dialog,
	DialogActions,
	DialogContent,
	DialogContentText,
	DialogTitle,
	Divider,
	<PERSON>ack,
	TextField,
	Typography
} from "@mui/material";
import {DataGrid, GridColDef} from "@mui/x-data-grid";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import {useEffect, useState} from "react";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import IconButton from "@mui/material/IconButton";
import {AddCircleOutline, Delete} from "@mui/icons-material";
import {useSnackbar} from "notistack";

interface PageData {
	status_code: number
	status_text: string

	salutation_words: SalutationWords[]
}

interface SalutationWords {
	id: number
	name: string
}

export default function AdminSalutations() {
	const {enqueueSnackbar} = useSnackbar();

	const [pageData, setPageData] = useState<PageData>();
	const [selectedRows, setSelectedRows] = useState<number[]>([]);
	const [actionsDisabled, setActionsDisabled] = useState(false);
	const [openNewWordsDialog, setOpenNewWordsDialog] = useState(false);

	// Query for fetching salutation words data.
	const pageDataQuery = useQuery({
		queryKey: ["salutationWordsPageData"],
		queryFn: () => authenticateAndFetchData("/admin/get-salutation-words/"),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	})
	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data);
		}
	}, [pageDataQuery.data]);

	// Mutation for deleting one word.
	const deleteWordMutation = useMutation({
		mutationKey: ["deleteSingleSalutationWord"],
		mutationFn: (id: number) => authenticateAndPostData("/admin/delete-salutation-word/", {
			salutation_word_id: id,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			pageDataQuery.refetch().then();
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	// Mutation for deleting multiple words.
	const deleteSelectedWordsMutation = useMutation({
		mutationKey: ["deleteMultipleSalutationWords"],
		mutationFn: (ids: number[]) => authenticateAndPostData("/admin/delete-salutation-words/", {
			salutation_word_ids: ids,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			pageDataQuery.refetch().then();
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	// Enabled/disables actions.
	useEffect(() => {
		setActionsDisabled(
			(selectedRows.length === 0) || deleteWordMutation.isPending || deleteSelectedWordsMutation.isPending
		)
	}, [selectedRows, deleteWordMutation.isPending, deleteSelectedWordsMutation.isPending]);

	const columns: GridColDef<SalutationWords>[] = [
		{
			field: "name",
			headerName: "Friendly Salutation Words",
			flex: 1,
			minWidth: 200,
			hideable: false,
			editable: false,
			filterable: true,
			sortable: true,
		},
		{
			field: "delete",
			headerName: "Delete",
			filterable: false,
			sortable: false,
			hideable: false,
			editable: false,
			renderCell: (params) => {
				return (
					<IconButton
						color="error"
						size="small"
						onClick={() => handleDeleteSingle(params.row.id)}
					>
						<Delete/>
					</IconButton>
				)
			}
		}
	];

	function handleDeleteSingle(id: number) {
		deleteWordMutation.mutate(id);
	}

	function handleDeleteSelected(ids: number[]) {
		deleteSelectedWordsMutation.mutate(ids);
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={2} sx={{pb: 4}}>
				<Typography fontWeight={"bold"} variant={"h5"}>Manage Friendly Salutation Words</Typography>
				<Divider/>
				<Stack direction={"column"} spacing={2}>
					{/* Table Actions */}
					<Box display={"flex"} flexDirection={"row"} justifyContent={"space-between"}>
						<Stack direction={"row"} spacing={2}>
							<Button variant={"contained"}
											size={"small"}
											startIcon={<AddCircleOutline/>}
											disabled={false}
											onClick={() => setOpenNewWordsDialog(true)}>
								Add New Words
							</Button>
							<Button color={"error"}
											variant={"contained"}
											size={"small"}
											startIcon={<Delete/>}
											disabled={actionsDisabled}
											onClick={() => {
												handleDeleteSelected(selectedRows);
											}}>
								Delete Selected
							</Button>
						</Stack>
					</Box>
					{/* Table */}
					<Box sx={{width: '100%'}}>
						<DataGrid
							rows={pageData.salutation_words}
							columns={columns}
							initialState={{
								pagination: {
									paginationModel: {
										pageSize: 10,
									},
								},
							}}
							pageSizeOptions={[10, 50, 100]}
							checkboxSelection
							disableRowSelectionOnClick
							onRowSelectionModelChange={rowSelectionModel => {
								setSelectedRows(rowSelectionModel as number[])
							}}
						/>
					</Box>
				</Stack>

				<AddNewWordsDialog open={openNewWordsDialog}
													 onClose={() => setOpenNewWordsDialog(false)}
													 onSuccess={() => {
														 setOpenNewWordsDialog(false);
														 pageDataQuery.refetch().then();
													 }}/>
			</Stack>
		)
	} else {
		// Ideally it should not reach here.
		return <></>
	}
}

function AddNewWordsDialog(props: {
	open: boolean,
	onClose: () => void,
	onSuccess: () => void,
}) {
	const {enqueueSnackbar} = useSnackbar();

	const [salutationWordsText, setSalutationWordsText] = useState<string>('');

	// Mutation to add salutation words.
	const addSalutationWordsMutation = useMutation({
		mutationKey: ["addNewSalutationWords"],
		mutationFn: (salutationWords: string[]) => authenticateAndPostData("/admin/add-salutation-words/", {
			salutation_words: salutationWords
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			setSalutationWordsText("");
			props.onSuccess();
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	function handleSubmit() {
		let salutationWords: string[] = salutationWordsText.trim().split("\n");
		if (salutationWords.length > 0) {
			addSalutationWordsMutation.mutate(salutationWords);
		}
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<Dialog open={props.open} onClose={props.onClose} maxWidth="sm" fullWidth>
			<DialogTitle>Add Salutation Words</DialogTitle>
			<DialogContent>
				<DialogContentText sx={{mb: 2}}>
					Enter new Friendly Salutation words for Spam Rate Score feature, one per line:
				</DialogContentText>
				<TextField
					autoFocus
					multiline
					rows={8}
					fullWidth
					variant="outlined"
					placeholder="Enter words here..."
					value={salutationWordsText}
					onChange={(e) => setSalutationWordsText(e.target.value)}
				/>
			</DialogContent>
			<DialogActions>
				<Button onClick={props.onClose}
								color={"primary"}
								disabled={addSalutationWordsMutation.isPending}>
					Cancel
				</Button>
				<Button color={"primary"}
								variant={"contained"}
								onClick={handleSubmit}
								disabled={addSalutationWordsMutation.isPending || !salutationWordsText.trim()}>
					Add Words
				</Button>
			</DialogActions>
		</Dialog>
	);
}
