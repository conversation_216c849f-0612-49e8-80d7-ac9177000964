import {<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Typography} from "@mui/material";
import {AddCircleOutline} from "@mui/icons-material";
import {useEffect, useState} from "react";
import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import {useQuery} from "@tanstack/react-query";
import {ColDef, ValueFormatterParams} from "ag-grid-community";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import ServerSideDataTable from "@components/ServerSideDataTable";
import {Link, useNavigate} from "react-router-dom";
import {urls} from "@routes";
import {CustomCellRendererProps} from "ag-grid-react";
import { format } from "date-fns";

interface PageData {
	status_code: number
	status_text: string

	plans: Plan[]
}

interface Plan {
	id: number
	name: string
	created_on_ts: number
	monthly_amount: number
	annual_amount: number
	popular: boolean
	hidden: boolean
	is_free_plan: boolean
	monthly_email_sending_quota: number
}


export default function AdminSubscriptionPlans() {
	const navigate = useNavigate();

	const [pageData, setPageData] = useState<PageData>();

	// Query for fetching subscription plans data.
	const pageDataQuery = useQuery({
		queryKey: ["adminAllSubscriptionPlansPageData"],
		queryFn: () => authenticateAndFetchData("/admin/all-subscription-plans/"),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	})
	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data);
		}
	}, [pageDataQuery.data]);

	function timestampFormatter(params: ValueFormatterParams) {
		if (params.value !== null) {
			return format(new Date(params.value), "do MMM y, HH:mm:ss (xxx)");
		} else {
			return "---"
		}
	}

	const EditButton = (props: CustomCellRendererProps) => {
		return (
			<Button component={Link} to={urls["adminEditSubscriptionPlan"].replace(":planId", props.data["id"])}>
				Edit
			</Button>
		)
	}

	// Columns for user table.
	const columnDefs: ColDef[] = [
		{field: "name", headerName: "Plan Name"},
		{field: "created_on_ts", headerName: "Created On", valueFormatter: timestampFormatter},
		{field: "monthly_amount", headerName: "Monthly Price ($)"},
		{field: "annual_amount", headerName: "Annual Price ($)"},
		{field: "popular", headerName: "Popular"},
		{field: "hidden", headerName: "Hidden"},
		{field: "is_free_plan", headerName: "Default Free Plan"},
		{field: "monthly_email_sending_quota", headerName: "Monthly Email Quota"},
		{headerName: "", resizable: false, cellRenderer: EditButton},
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={2} sx={{pb: 4, height: "100%"}}>
				<Typography fontWeight={"bold"} variant={"h5"}>Manage Subscription Plans</Typography>
				<Divider/>
				<Stack direction={"column"} spacing={2} sx={{height: "100%"}}>
					{/* Table Actions */}
					<Box display={"flex"} flexDirection={"row"} justifyContent={"space-between"}>
						<Stack direction={"row"} spacing={2}>
							<Button variant={"contained"}
											size={"small"}
											startIcon={<AddCircleOutline/>}
											disabled={false}
											onClick={() => {
												navigate(urls["adminCreateSubscriptionPlans"]);
											}}>
								Add New Plan
							</Button>
						</Stack>
					</Box>
					{/* Table */}
					<Box sx={{height: "100%"}}>
						<ServerSideDataTable columns={columnDefs} rows={pageData.plans}/>
					</Box>
				</Stack>
			</Stack>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
