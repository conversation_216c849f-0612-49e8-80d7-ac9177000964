import {<PERSON>, <PERSON><PERSON>, Chip, DialogContentText, Di<PERSON>r, Stack, Tooltip, Typography, useTheme, Menu, MenuItem, Dialog, DialogContent, DialogTitle, DialogActions} from "@mui/material";
import ServerSideDataTable from "@components/ServerSideDataTable";
import * as React from "react";
import {useEffect, useState} from "react";
import {ColDef, ValueFormatterParams} from "ag-grid-community";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import {useMutation, useQuery} from "@tanstack/react-query";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import IconButton from "@mui/material/IconButton";
import {ArrowBack, Delete, MoreVert, Edit} from "@mui/icons-material";
import {useNavigate, useParams} from "react-router-dom";
import {CustomCellRendererProps} from "ag-grid-react";
import {format} from "date-fns";
import {useDialogs} from "@toolpad/core";
import {useSnackbar} from "notistack";
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon';
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { DateTime } from "luxon";


interface PageData {
	status_code: number
	status_text: string

	user_email: string
	connected_domains: ConnectedDomain[]
}

interface ConnectedDomain {
	id: number
	domain: string
	active_campaigns: Array<string>
	created_on_ts: number
	setup_complete: boolean
	current_setup_stage: number
	naming_strategy: string
	aws_hosted_zone_id: string | null
	sending_limit: number
	warmup_email_count: number
	redirect_subdomains_to: string | null
	blacklist_status: string[]
	workspace_name: string
	seconds_remaining: number
}


export default function AdminUserConnectedDomains() {
	const {userId} = useParams();
	const navigate = useNavigate();
	const theme = useTheme();
	const dialogs = useDialogs();
	const {enqueueSnackbar} = useSnackbar();

	const [pageData, setPageData] = useState<PageData>();
	const [editWarmupDateTime, setEditWarmupDateTime] = useState<any | null>(null);
	const [openEditWarmup, setOpenEditWarmup] = useState(false)							

	// Query to fetch all connected domains.
	const adminUserConnectedDomainsQuery = useQuery({
		queryKey: ["adminUserConnectedDomainsQuery", userId],
		queryFn: () => authenticateAndFetchData(`/admin/get-user-connected-domains/?user_id=${userId}`),
		gcTime: 0,
		refetchOnWindowFocus: false,
		retry: retryFn,
	});
	useEffect(() => {
		if (adminUserConnectedDomainsQuery.data) {
			setPageData(adminUserConnectedDomainsQuery.data.data as PageData);
		}
	}, [adminUserConnectedDomainsQuery.data]);

	// Mutation for deleting domain.
	const deleteDomainMutation = useMutation({
		mutationKey: ["adminDeleteDomainMutation"],
		mutationFn: (domain_id: string) => authenticateAndPostData("/admin/delete-domain/", {
			id: domain_id
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			enqueueSnackbar("Domain is being deleted. It might take a few seconds. Please refresh and check later.", {
				variant: "success",
			});
			// adminUserConnectedDomainsQuery.refetch().then();
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	})

	function timestampFormatter(params: ValueFormatterParams) {
		return format(new Date(params.value), "do MMM y, HH:mm:ss (xxx)");
	}

	function setupCompleteCellRenderer(props: CustomCellRendererProps) {
		return props.value ? <span style={{color: theme.palette.success.main}}>Yes</span> : <span>No</span>
	}

	const blackListStatus = (params: CustomCellRendererProps) => {
		const value = params.value;
		if (!value || value === "Healthy") {
			return (
				<Chip
					size="small"
					color="success"
					label="Healthy"
					sx={{fontSize: "0.75rem", height: 24}}
				/>
			);
		}

		if (value === "Incomplete") {
			return (
				<Chip
					size="small"
					color="default"
					label="Incomplete"
					sx={{fontSize: "0.75rem", height: 24}}
				/>
			);
		}

		if (value === "Unknown Error") {
			return (
				<Chip
					size="small"
					color="error"
					label="Unknown Error"
					sx={{fontSize: "0.75rem", height: 24}}
				/>
			);
		}

		const sources = typeof value === "string" ? value.split(",") : [];

		return (
			<Box sx={{mt: 1}}>
				<Stack spacing={0.5} sx={{maxWidth: 180}}>
					{sources.slice(0, 3).map((source: string, idx: number) => (
						<Tooltip title={source.trim()} key={idx}>
							<Chip
								size="small"
								color="warning"
								label={source.trim()}
								sx={{
									maxWidth: "100%",
									overflow: "hidden",
									textOverflow: "ellipsis",
									whiteSpace: "nowrap",
									fontSize: "0.75rem",
									height: 24,
								}}
							/>
						</Tooltip>
					))}
					{sources.length > 3 && (
						<Chip
							size="small"
							color="warning"
							label={`+${sources.length - 3} more`}
							sx={{fontSize: "0.75rem", height: 24}}
						/>
					)}
				</Stack>
			</Box>
		);
	};

	const ActionsButton = (props: CustomCellRendererProps) => {
		const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
		const open = Boolean(anchorEl);

		const handleClick = (event: any) => {
			setAnchorEl(event.currentTarget);
		};

		const handleClose = () => {
			setAnchorEl(null);
		};
		
		const handleDelete = async () => {
			if (props.data["active_campaigns"].length > 0) {
				const deleteConfirmed = await dialogs.confirm(
					(
						<DialogContentText sx={{ whiteSpace: "pre-line" }}>
							This will permanently remove <b>{props.data["domain"]}</b> from DeliverymanAI. 
							This domain is currently being used in the following active campaigns:
							<br/><br/>
							{props.data["active_campaigns"].join("\n")}
							<br/><br/>
							Proceed?
						</DialogContentText>
					),
					{
						title: `Confirm deleting ${props.data["domain"]}?`,
						okText: "Proceed",
					}
				);

				if (deleteConfirmed) {
					deleteDomainMutation.mutate(props.data["id"]);
				}
			} else {
				const deleteConfirmed = await dialogs.confirm(
					(
						<DialogContentText sx={{ whiteSpace: "pre-line" }}>
							This will permanently remove <b>{props.data["domain"]}</b> from DeliverymanAI. Proceed?
						</DialogContentText>
					),
					{
						title: `Confirm deleting ${props.data["domain"]}?`,
						okText: "Proceed",
					}
				);

				if (deleteConfirmed) {
					deleteDomainMutation.mutate(props.data["id"]);
				}
			}

			handleClose();
		};

		return (
			<>
				<IconButton
					aria-label="more"
					aria-controls={open ? "long-menu" : undefined}
					aria-expanded={open ? "true" : undefined}
					aria-haspopup="true"
					onClick={handleClick}
				>
					<MoreVert />
				</IconButton>
				<Menu
					id="long-menu"
					MenuListProps={{
						"aria-labelledby": "long-button",
					}}
					anchorEl={anchorEl}
					open={open}
					onClose={handleClose}
				>
					<MenuItem
						onClick={() => {
							setEditWarmupDateTime(props.data);
							setOpenEditWarmup(true);
						}}
					>
						<Edit color={"warning"} sx={{ mr: 1 }} />
						<Typography variant="subtitle2">Edit Warmup</Typography>
					</MenuItem>

					<MenuItem onClick={handleDelete}>
						<Delete color="error" sx={{ mr: 1 }} />
						<Typography variant="subtitle2">Delete Domain</Typography>
					</MenuItem>
				</Menu>
			</>
		);
	};


	// Columns for connected domains table.
	const columnDefs: ColDef[] = [
		{field: "domain", headerName: "Domain", flex: 2},
		{
			field: "created_on_ts",
			headerName: "Added On",
			valueFormatter: timestampFormatter,
			comparator: (valueA, valueB, nodeA, nodeB) => {
				return nodeA.data["created_on_ts"] > nodeB.data["created_on_ts"] ? 1 : -1;
			},
			flex: 2
		},
		{
			field: "setup_complete",
			headerName: "Setup Complete",
			cellRenderer: setupCompleteCellRenderer,
			getQuickFilterText: params => params.value ? "Yes" : "No",
			comparator: (valueA, valueB, nodeA, nodeB) => {
				let value1 = nodeA.data["setup_complete"] ? 1 : 0;
				let value2 = nodeB.data["setup_complete"] ? 1 : 0;

				if (value1 === value2) return 0;
				return value1 > value2 ? 1 : -1;
			}
		},
		{field: "current_setup_stage", headerName: "Setup Stage"},
		{field: "naming_strategy", headerName: "Naming Strategy"},
		{field: "aws_hosted_zone_id", headerName: "AWS Hosted Zone ID"},
		{field: "sending_limit", headerName: "Sending Limit"},
		{field: "blacklist_status", headerName: "Blacklist Status", cellRenderer: blackListStatus},
		{field: "warmup_email_count", headerName: "Warmup Email Limit"},
		{field: "workspace_name", headerName: "Workspace Name"},
		{field: "redirect_subdomains_to", headerName: "Domain Redirect"},
		{headerName: "Actions", cellRenderer: ActionsButton, sortable: false, resizable: false},
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (adminUserConnectedDomainsQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (adminUserConnectedDomainsQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={adminUserConnectedDomainsQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={2} sx={{pb: 4, height: "100%"}}>
				<Stack direction={"row"} spacing={2} alignItems={"center"}>
					<IconButton onClick={() => navigate(-1)}><ArrowBack/></IconButton>
					<Typography fontWeight={"bold"} variant={"h5"}>Connected Domains ({pageData.user_email})</Typography>
				</Stack>
				<Divider/>
				<Typography variant={"h5"}>
					⚠️ IMPORTANT: Please press delete button for each domain only ONCE.
					It can take around 30-60 seconds for some domains to get deleted. Refresh the page every few seconds to check.
				</Typography>
				<Box sx={{height: "100%"}}>
					<ServerSideDataTable columns={columnDefs} rows={pageData.connected_domains}
										 getRowHeight={(params) => {
															const value = params?.data?.black_list_status;
															if (!value || value === "Healthy" || value === "Unknown Error") {
															return 42;
															}
															const sources = typeof value === "string" ? value.split(",") : [];
															const visibleChips = Math.min(sources.length, 4);
															return visibleChips * 32 + 8;
															}}
					/>
				</Box>
				<EditWrampDateTime open={openEditWarmup}					
					warmupDatetime={editWarmupDateTime}					
					onClose={() => setOpenEditWarmup(false)}
					onSuccess={() => {
						setOpenEditWarmup(false);
						adminUserConnectedDomainsQuery.refetch().then();
				}}/>
			</Stack>
		)
	} else {
		// Ideally it should not reach here.
		return <></>
	}
}


function EditWrampDateTime(props: {
  open: boolean;  
  warmupDatetime: ConnectedDomain;
  onClose: () => void;
  onSuccess: () => void;  
}) {
  	const { enqueueSnackbar } = useSnackbar();	
	
	const [newDate, setNewDate] = useState<DateTime | null>(null);

	useEffect(() => {
		if (props.warmupDatetime?.seconds_remaining != null) {
			setNewDate(
			DateTime.now().plus({ seconds: Number(props.warmupDatetime.seconds_remaining) })
			);
		}
	}, [props.open, props.warmupDatetime]);	

  const WarmupDateTimeMutation = useMutation({
    mutationKey: ["WarmupDateTime"],
    mutationFn: () =>
      authenticateAndPostData("/admin/admin-update-warmup-date/", {
        id: props.warmupDatetime.id,
        warmup_datetime: newDate ? newDate.toISO() : null,
      }),
    gcTime: 0,
    retry: retryFn,
    onSuccess: () => {
      props.onSuccess();
      enqueueSnackbar("Campaign start time updated successfully!", {
        variant: "success",
      });
    },
    onError: (error: ApiRequestFailed) => {
      enqueueSnackbar(error.data.message, {
        variant: "error",
      });
      console.error(error);
    },
  });

  const handleSave = async () => {
	if (!newDate) return;

	if (newDate < DateTime.now()) {
		enqueueSnackbar("Please select a future date & time.", { variant: "error" });
		return;
	}

	WarmupDateTimeMutation.mutate();
	};


  return (
	<LocalizationProvider dateAdapter={AdapterLuxon}>
    <Dialog onClose={props.onClose} open={props.open} maxWidth="sm" fullWidth>
      <DialogTitle>Update Warmup Date Time</DialogTitle>
      <DialogContent>
	  <DateTimePicker
		slotProps={{
			textField: {
			placeholder: "MM/DD/YYYY hh:mm a",
			label: "",
			InputLabelProps: { shrink: false },
			},
		}}
		value={newDate}
		onChange={(val) => setNewDate(val)}
		minDateTime={DateTime.now()}
		/>
      </DialogContent>
      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button onClick={props.onClose} disabled={WarmupDateTimeMutation.isPending}>
          Cancel
        </Button>
        <Button onClick={handleSave} variant="contained" disabled={!newDate}>
          Save
        </Button>
      </DialogActions>
    </Dialog>
	</LocalizationProvider>
  );
}
