import {Box, Divider, Stack, Typography} from "@mui/material";
import ServerSideDataTable from "@components/ServerSideDataTable";
import {useEffect, useState} from "react";
import {ColDef, ValueFormatterParams} from "ag-grid-community";
import {ApiRequestFailed, authenticateAndFetchData, retryFn} from "@lib/apis";
import {useQuery} from "@tanstack/react-query";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import IconButton from "@mui/material/IconButton";
import {ArrowBack} from "@mui/icons-material";
import {useNavigate, useParams} from "react-router-dom";
import {format} from "date-fns";


interface PageData {
	status_code: number
	status_text: string

	user_email: string
	workspaces: Workspace[]
}

interface Workspace {
	id: number
	name: string
	created_on_ts: number
	total_emails_sent: number
	total_replies_received: number
}


export default function AdminUserWorkspaces() {
	const {userId} = useParams();
	const navigate = useNavigate();

	const [pageData, setPageData] = useState<PageData>();

	// Query to fetch all workspaces.
	const adminUserWorkspacesQuery = useQuery({
		queryKey: ["adminUserWorkspacesQuery", userId],
		queryFn: () => authenticateAndFetchData(`/admin/get-user-workspaces/?user_id=${userId}`),
		gcTime: 0,
		refetchOnWindowFocus: false,
		retry: retryFn,
	});
	useEffect(() => {
		if (adminUserWorkspacesQuery.data) {
			setPageData(adminUserWorkspacesQuery.data.data as PageData);
		}
	}, [adminUserWorkspacesQuery.data]);

	function timestampFormatter(params: ValueFormatterParams) {
		return format(new Date(params.value), "do MMM y, HH:mm:ss (xxx)");
	}

	// Columns for workspaces table.
	const columnDefs: ColDef[] = [
		{field: "name", headerName: "Workspace Name"},
		{field: "created_on_ts", headerName: "Created On", valueFormatter: timestampFormatter},
		{field: "total_emails_sent", headerName: "Total Emails Sent"},
		{field: "total_replies_received", headerName: "Total Replies Received", resizable: false},
	]

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (adminUserWorkspacesQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (adminUserWorkspacesQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={adminUserWorkspacesQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={2} sx={{pb: 4, height: "100%"}}>
				<Stack direction={"row"} spacing={2} alignItems={"center"}>
					<IconButton onClick={() => navigate(-1)}><ArrowBack/></IconButton>
					<Typography fontWeight={"bold"} variant={"h5"}>Workspaces ({pageData.user_email})</Typography>
				</Stack>
				<Divider/>
				<Box sx={{height: "100%"}}>
					<ServerSideDataTable columns={columnDefs} rows={pageData.workspaces}/>
				</Box>
			</Stack>
		)
	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
