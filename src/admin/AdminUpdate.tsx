import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import {useEffect, useState} from "react";
import {useMutation, useQuery} from "@tanstack/react-query";
import {useSnackbar} from "notistack";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {Box, Button, Typography} from "@mui/material";
import {ColDef, ValueFormatterParams} from "ag-grid-community";
import {CustomCellRendererProps} from "ag-grid-react";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import Dialog from "@mui/material/Dialog";
import ServerSideDataTable from "@components/ServerSideDataTable";
import {
    DialogContentText,
    DialogTitle,
    Divider,
    Stack,
    TextField,  
    Menu,
	MenuItem,  
} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import {AddCircleOutline, Delete, Edit, MoreVert} from "@mui/icons-material";
import {DateTime} from 'luxon';
import {DateTimePicker, LocalizationProvider} from '@mui/x-date-pickers';
import {AdapterLuxon} from '@mui/x-date-pickers/AdapterLuxon'
import { format } from "date-fns";
import {useDialogs} from "@toolpad/core";

interface PageData {
    id: string
    title: string,
    description: string,
    created_at: string,  
}

export default function AdminUpdate() {
    const { enqueueSnackbar } = useSnackbar();
    const dialogs = useDialogs();
    
    const [pageData, setPageData] = useState<PageData[]>([]);
    const [openAddUpdatePost, setOpenAddUpdatePost] = useState(false)
    const [editingPostData, setEditingPostData] = useState<any | null>(null);
    const [selectedRows, setSelectedRows] = useState<any[]>([]);
    const [
            campaignCustomStartDatetime,
            setCampaignCustomStartDatetime
        ] = useState<DateTime | null>(null);

    const pageDataQuery = useQuery({
		queryKey: ["UpdatePostsPageData"],
		queryFn: () => authenticateAndFetchData("/admin/admin-get-update-posts/"),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	})
    
	useEffect(() => {
		if (pageDataQuery.data) {            
			setPageData(pageDataQuery.data.data.data);
		}
	}, [pageDataQuery.data]);

    const deleteUpdatePostMutation = useMutation({
		mutationKey: ["deleteUpdatePost"],
		mutationFn: (ids: number[]) => authenticateAndPostData("/admin/admin-delete-udpate-post/", {
			updatePostIds: ids,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
            pageDataQuery.refetch().then();
            enqueueSnackbar("Successfully Deleted the update post.", {
				variant: "success",
			});
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

    function timestampFormatter(params: ValueFormatterParams) {            
            if (params.value !== null) {
                return format(new Date(params.value), "do MMM y, HH:mm:ss (xxx)");
            } else {
                return "---"
            }
        }

	function handleDeleteSelected(ids: number[]) {
        deleteUpdatePostMutation.mutate(ids);
    }

    const ActionsButton = (props: CustomCellRendererProps) => {
		const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
		const open = Boolean(anchorEl);

		const handleClick = (event: any) => {
			setAnchorEl(event.currentTarget);
		};

		const handleClose = () => {
			setAnchorEl(null);
		};


		const handleDelete = async () => {
			const cancelConfirmed = await dialogs.confirm(
				`Are you sure you want to Update Post "${props.data['title']}"?`,
				{
					title: "Delete Update Post",
					cancelText: "Cancel",
					okText: "Confirm",
				}
			);
			if (cancelConfirmed) {
				deleteUpdatePostMutation.mutate([props.data["id"]]);
			}
			handleClose();
		};

		return (
			<>
				<IconButton
					aria-label="more"
					aria-controls={open ? 'long-menu' : undefined}
					aria-expanded={open ? 'true' : undefined}
					aria-haspopup="true"
					onClick={handleClick}
				>
					<MoreVert/>
				</IconButton>
				<Menu
					id="long-menu"
					MenuListProps={{
						'aria-labelledby': 'long-button',
					}}
					anchorEl={anchorEl}
					open={open}
					onClose={handleClose}
				>					
					<MenuItem onClick={() => {
                        setEditingPostData(props.data);
                        setOpenAddUpdatePost(true);
                    }}>

						<Edit color={"warning"} sx={{mr: 1}}/>
						<Typography variant="subtitle2">Edit Update</Typography>
					</MenuItem>
					{<MenuItem onClick={handleDelete} >
						<Delete color="error" sx={{ mr: 1 }}/>
						<Typography variant="subtitle2">Delete Update</Typography>
					</MenuItem>}
				</Menu>
			</>
		);
	}

    const columnDefs: ColDef[] = [                  
            {field: "title", headerName: "Title"},
            {field: "created_at", headerName: "Created On", valueFormatter: timestampFormatter},        
            {headerName: "Actions", cellRenderer: ActionsButton, sortable: false, resizable: false},
        ]

    if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={2} sx={{pb: 4, height: "100%"}}>
				<Typography fontWeight={"bold"} variant={"h5"}>Manage Update Section</Typography>
				<Divider/>
				<Stack direction={"column"} spacing={2} sx={{height: "100%"}}>					
					<Box display={"flex"} flexDirection={"row"} justifyContent={"space-between"}>
						<Stack direction={"row"} spacing={2}>
							<Button variant={"contained"}
											size={"small"}
											startIcon={<AddCircleOutline/>}
											disabled={false}
											onClick={() => {
                                                setOpenAddUpdatePost(true)
                                                setEditingPostData(null);
                                            }}>
								Create Update Post
							</Button>
							<Button color={"error"}
											variant={"contained"}
											size={"small"}
											startIcon={<Delete/>}
											disabled={selectedRows.length === 0}
                                            onClick={() => {
                                                const ids = selectedRows.map(row => row.id);
                                                handleDeleteSelected(ids);
                                            }}>
								Delete Selected
							</Button>
						</Stack>
					</Box>
					{/* Table */}
					<Box sx={{height: "100%"}}>
                        <ServerSideDataTable columns={columnDefs} rows={pageData} onSelectionChange={(rows) => {setSelectedRows(rows); }} rowSelection={{ mode: "multiRow" }} noRowsText={"No Update Posts Available"}/>
                    </Box>
				</Stack>		
                <AddNewAndEditUpdatePost open={openAddUpdatePost}
                        campaignCustomStartDatetime={campaignCustomStartDatetime}
                        editingData={editingPostData}
                        onClose={() => setOpenAddUpdatePost(false)}
                        onSuccess={() => {
                            setOpenAddUpdatePost(false);
                            pageDataQuery.refetch().then();
                        }}/>		
			</Stack>
		)
	} else {
		return <></>
	}
}


function AddNewAndEditUpdatePost(props: {
    open: boolean,
    editingData: any | null,
    campaignCustomStartDatetime: DateTime | null,
    onClose: () => void,
    onSuccess: () => void,
}) {
    const { enqueueSnackbar } = useSnackbar();

    const [title, setTitle] = useState<string>('');
    const [description, setDescription] = useState<string>('');
    const [datetime, setDatetime] = useState<DateTime | null>(props.campaignCustomStartDatetime);


    useEffect(() => {
        if (props.editingData) {
            setTitle(props.editingData.title || '');
            setDescription(props.editingData.description || '');            
            setDatetime(DateTime.fromISO(props.editingData.created_at));
        } else {
            setTitle('');
            setDescription('');
            setDatetime(null);
        }
    }, [props.editingData]);
    
    const postMutation = useMutation({
        mutationKey: ["addOrEditUpdatePost"],
        mutationFn: async ({ id, title, description, date }: { id?: number; title: string; description: string; date: string }) => {
            const url = id ? "/admin/admin-edit-update-post/" : "/admin/admin-add-update-post/";
            const payload = id
                ? { id, title, description, date }
                : { title, description, date };

            return await authenticateAndPostData(url, payload);
        },
        gcTime: 0,
        retry: retryFn,
        onSuccess: (_data, variables) => {
            setTitle('');
            setDescription('');
            setDatetime(null);
            props.onSuccess();
            enqueueSnackbar(`Successfully ${variables.id ? "Updated" : "Created"} the update post.`, {
            variant: "success",
            });
        },
        onError: (error: ApiRequestFailed) => {
            console.error(error);
            enqueueSnackbar(error.data.message, { variant: 'error' });
        },
    });


    function handleSubmit() {
        if (!title.trim() || !description.trim() || !datetime) {
            enqueueSnackbar("Please fill in all fields.", { variant: "warning" });
            return;
        }

        postMutation.mutate({
            id: props.editingData?.id,
            title,
            description,
            date: datetime.toISO({ suppressMilliseconds: true, suppressSeconds: true }) ?? "",
        });
    }


    return (
        <Dialog open={props.open} onClose={props.onClose} maxWidth="sm" fullWidth>
            <DialogTitle>Add or Edit Update Post</DialogTitle>
            <DialogContent>
                <DialogContentText sx={{ mb: 1 }}>Title:</DialogContentText>
                <TextField
                    fullWidth
                    variant="outlined"
                    placeholder="Enter title..."
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                />

                <DialogContentText sx={{ mt: 3, mb: 1 }}>Description:</DialogContentText>
                <TextField
                    fullWidth
                    multiline
                    rows={5}
                    variant="outlined"
                    placeholder="Enter description..."
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                />

                <LocalizationProvider dateAdapter={AdapterLuxon}>
                <Typography variant="body1" sx={{ mt: 3, fontWeight: "bold" }}>
                    Timezone: {DateTime.local().zoneName}
                </Typography>
                <DateTimePicker
                    value={datetime}
                    onChange={(val) => setDatetime(val)}
                    timezone={DateTime.local().zoneName}
                    sx={{ mt: 2 }}
                />
                </LocalizationProvider>
            </DialogContent>

            <DialogActions sx={{ px: 3, pb: 2 }}>
                <Button onClick={props.onClose} disabled={postMutation.isPending}>
                    Cancel
                </Button>
                <Button
                    variant="contained"
                    onClick={handleSubmit}
                    disabled={postMutation.isPending || !title.trim() || !description.trim() || !datetime}
                >
                    {props.editingData ? "Update Post" : "Add Update Post"}
                </Button>

            </DialogActions>
        </Dialog>
    );
}
