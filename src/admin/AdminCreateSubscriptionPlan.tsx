import {<PERSON><PERSON>, <PERSON>box, Divider, FormControl<PERSON>abel, Stack, <PERSON>Field, Typography} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import {ArrowBack, Publish} from "@mui/icons-material";
import {useNavigate} from "react-router-dom";
import {ChangeEvent, useEffect, useState} from "react";
import {useSnackbar} from "notistack";
import {useMutation, useQuery} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndFetchData, authenticateAndPostData, retryFn} from "@lib/apis";
import PageLoading from "@components/PageLoading";
import PageDataErrorHandler from "@components/PageDataErrorHandler";
import {urls} from "@routes";

interface PageData {
	status_code: number
	status_text: string
}

export default function AdminCreateSubscriptionPlan() {
	const {enqueueSnackbar} = useSnackbar();
	const navigate = useNavigate();

	const [pageData, setPageData] = useState<PageData>();
	const [isFreePlan, setIsFreePlan] = useState(false);
	const [planName, setPlanName] = useState("");
	const [displayOrder, setDisplayOrder] = useState("");
	const [productId, setProductId] = useState("");
	const [monthlyPriceId, setMonthlyPriceId] = useState("");
	const [annualPriceId, setAnnualPriceId] = useState("");
	const [monthlyAmount, setMonthlyAmount] = useState("");
	const [annualAmount, setAnnualAmount] = useState("");
	const [monthlyFeatureList, setMonthlyFeatureList] = useState("");
	const [annualFeatureList, setAnnualFeatureList] = useState("");
	const [popular, setPopular] = useState<boolean>(false);
	const [hidePlan, setHidePlan] = useState<boolean>(false);
	const [monthlyEmailQuota, setMonthlyEmailQuota] = useState("");
	const [annualEmailQuota, setAnnualEmailQuota] = useState("");
	const [domainConnectionLimit, setDomainConnectionLimit] = useState("");

	// Query for only authenticating user.
	const pageDataQuery = useQuery({
		queryKey: ["adminCreateSubscriptionPlanPageData"],
		queryFn: () => authenticateAndFetchData("/admin/create-new-subscription-plan/"),
		gcTime: 0,
		retry: retryFn,
		refetchOnWindowFocus: false,
	})
	useEffect(() => {
		if (pageDataQuery.data) {
			setPageData(pageDataQuery.data.data);
		}
	}, [pageDataQuery.data]);

	// Mutation for creating new plan.
	const createPlanMutation = useMutation({
		mutationKey: ["createNewSubscriptionPlanMutation"],
		mutationFn: () => authenticateAndPostData("/admin/create-new-subscription-plan/", {
			is_free_plan: isFreePlan,
			plan_display_name: planName,
			display_order: displayOrder,
			product_id: productId,
			monthly_price_id: monthlyPriceId,
			annual_price_id: annualPriceId,
			monthly_amount: monthlyAmount,
			annual_amount: annualAmount,
			monthly_feature_list: monthlyFeatureList,
			annual_feature_list: annualFeatureList,
			monthly_email_sending_quota: monthlyEmailQuota,
			annual_email_sending_quota: annualEmailQuota,
			total_domain_connections_allowed: domainConnectionLimit,
			popular_plan: popular,
			hidden: hidePlan,
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: () => {
			navigate(urls["adminSubscriptionPlans"]);
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			enqueueSnackbar(error.data.message, {
				variant: "error",
			});
		}
	});

	function displayOrderHandler(event: ChangeEvent<HTMLInputElement>) {
		// Only allow numbers.
		const newValue = event.target.value;
		if (newValue === '' || /^[0-9\b]+$/.test(newValue)) {
			setDisplayOrder(newValue);
		}
	}

	function monthlyAmountHandler(event: ChangeEvent<HTMLInputElement>) {
		// Only allow numbers.
		const newValue = event.target.value;
		if (newValue === '' || /^[0-9\b]+$/.test(newValue)) {
			setMonthlyAmount(newValue);
		}
	}

	function annualAmountHandler(event: ChangeEvent<HTMLInputElement>) {
		// Only allow numbers.
		const newValue = event.target.value;
		if (newValue === '' || /^[0-9\b]+$/.test(newValue)) {
			setAnnualAmount(newValue);
		}
	}

	function monthlyEmailQuotaHandler(event: ChangeEvent<HTMLInputElement>) {
		// Only allow numbers.
		const newValue = event.target.value;
		if (newValue === '' || /^[0-9\b]+$/.test(newValue)) {
			setMonthlyEmailQuota(newValue);
		}
	}

	function annualEmailQuotaHandler(event: ChangeEvent<HTMLInputElement>) {
		// Only allow numbers.
		const newValue = event.target.value;
		if (newValue === '' || /^[0-9\b]+$/.test(newValue)) {
			setAnnualEmailQuota(newValue);
		}
	}

	function domainConnectionLimitHandler(event: ChangeEvent<HTMLInputElement>) {
		// Only allow numbers.
		const newValue = event.target.value;
		if (newValue === '' || /^[0-9\b]+$/.test(newValue)) {
			setDomainConnectionLimit(newValue);
		}
	}

	function createPlanHandler() {
		if (planName && displayOrder && monthlyAmount && annualAmount && monthlyEmailQuota) {
			createPlanMutation.mutate();
		} else {
			enqueueSnackbar("Please fill in all required fields.", {
				variant: "error",
			});
		}
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	if (pageDataQuery.isLoading) {
		return (
			<PageLoading/>
		)

	} else if (pageDataQuery.error as unknown as ApiRequestFailed) {
		return <PageDataErrorHandler error={pageDataQuery.error as unknown as ApiRequestFailed}/>

	} else if (pageData) {
		return (
			<Stack direction={"column"} spacing={2} sx={{pb: 4}}>
				<Stack direction={"row"} spacing={2} alignItems={"center"}>
					<IconButton onClick={() => navigate(-1)}><ArrowBack/></IconButton>
					<Typography fontWeight={"bold"} variant={"h5"}>Create New Subscription Plan</Typography>
				</Stack>
				<Divider/>
				<Stack direction={"column"} spacing={4}>
					{/* ------------- IS FREE PLAN ------------- */}
					<Stack direction={"column"} spacing={1}>
						<FormControlLabel control={<Checkbox checked={isFreePlan} onChange={e => setIsFreePlan(e.target.checked)}/>}
															label={"Is Default Free Plan"}/>
					</Stack>
					{/* ------------- PLAN NAME ------------- */}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
							Plan Display Name:
						</Typography>
						<TextField value={planName}
											 onChange={e => setPlanName(e.target.value)}
											 variant="outlined"
											 placeholder={"Basic"}
											 sx={{maxWidth: "500px"}}
											 required/>
					</Stack>
					{/* ------------- MONTHLY AMOUNT ------------- */}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
							Display Order:
						</Typography>
						<TextField value={displayOrder}
											 onChange={displayOrderHandler}
											 variant="outlined"
											 sx={{maxWidth: "150px"}}
											 placeholder={"0"}
											 slotProps={{
												 htmlInput: {
													 inputMode: 'numeric',
													 pattern: '[0-9]*'
												 }
											 }}
											 required/>
					</Stack>
					{/* ------------- PRODUCT ID ------------- */}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
							Stripe Product ID:
						</Typography>
						<TextField value={productId}
											 onChange={e => setProductId(e.target.value)}
											 variant="outlined"
											 placeholder={"prod_xxxxxxxxxxxxxx"}
											 sx={{maxWidth: "350px"}}/>
					</Stack>
					{/* ------------- MONTHLY PRICE ID ------------- */}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
							Monthly Price ID:
						</Typography>
						<TextField value={monthlyPriceId}
											 onChange={e => setMonthlyPriceId(e.target.value)}
											 variant="outlined"
											 placeholder={"price_xxxxxxxxxxxxxx"}
											 sx={{maxWidth: "350px"}}/>
					</Stack>
					{/* ------------- ANNUAL PRICE ID ------------- */}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
							Annual Price ID:
						</Typography>
						<TextField value={annualPriceId}
											 onChange={e => setAnnualPriceId(e.target.value)}
											 variant="outlined"
											 placeholder={"price_xxxxxxxxxxxxxx"}
											 sx={{maxWidth: "350px"}}/>
					</Stack>
					{/* ------------- MONTHLY AMOUNT ------------- */}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
							Monthly Amount (USD):
						</Typography>
						<TextField value={monthlyAmount}
											 onChange={monthlyAmountHandler}
											 variant="outlined"
											 sx={{maxWidth: "150px"}}
											 placeholder={"0"}
											 slotProps={{
												 htmlInput: {
													 inputMode: 'numeric',
													 pattern: '[0-9]*'
												 }
											 }}
											 required/>
					</Stack>
					{/* ------------- ANNUAL AMOUNT ------------- */}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
							Annual Amount (USD):
						</Typography>
						<TextField value={annualAmount}
											 onChange={annualAmountHandler}
											 variant="outlined"
											 sx={{maxWidth: "150px"}}
											 placeholder={"0"}
											 slotProps={{
												 htmlInput: {
													 inputMode: 'numeric',
													 pattern: '[0-9]*'
												 }
											 }}
											 required/>
					</Stack>
					{/* ------------- MONTHLY FEATURE LIST ------------- */}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
							Monthly Feature List (one on each line):
						</Typography>
						<TextField value={monthlyFeatureList}
											 onChange={e => setMonthlyFeatureList(e.target.value)}
											 variant="outlined"
											 sx={{maxWidth: "600px"}}
											 placeholder={"Features..."}
											 multiline={true}
											 rows={10}
											 required/>
					</Stack>
					{/* ------------- ANNUAL FEATURE LIST ------------- */}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
							Annual Feature List (one on each line):
						</Typography>
						<TextField value={annualFeatureList}
											 onChange={e => setAnnualFeatureList(e.target.value)}
											 variant="outlined"
											 sx={{maxWidth: "600px"}}
											 placeholder={"Features..."}
											 multiline={true}
											 rows={10}
											 required/>
					</Stack>
					{/* ------------- MONTHLY EMAIL QUOTA ------------- */}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
							Monthly Email Sending Quota:
						</Typography>
						<TextField value={monthlyEmailQuota}
											 onChange={monthlyEmailQuotaHandler}
											 variant="outlined"
											 sx={{maxWidth: "200px"}}
											 placeholder={"0"}
											 slotProps={{
												 htmlInput: {
													 inputMode: 'numeric',
													 pattern: '[0-9]*'
												 }
											 }}
											 required/>
					</Stack>
					{/* ------------- ANNUAL EMAIL QUOTA ------------- */}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
							Annual Email Sending Quota:
						</Typography>
						<TextField value={annualEmailQuota}
											 onChange={annualEmailQuotaHandler}
											 variant="outlined"
											 sx={{maxWidth: "200px"}}
											 placeholder={"0"}
											 slotProps={{
												 htmlInput: {
													 inputMode: 'numeric',
													 pattern: '[0-9]*'
												 }
											 }}
											 required/>
					</Stack>
					{/* ------------- DOMAIN CONNECTION LIMIT ------------- */}
					<Stack direction={"column"} spacing={1}>
						<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
							Domain Connection Limit:
						</Typography>
						<TextField value={domainConnectionLimit}
											 onChange={domainConnectionLimitHandler}
											 variant="outlined"
											 sx={{maxWidth: "200px"}}
											 placeholder={"0"}
											 slotProps={{
												 htmlInput: {
													 inputMode: 'numeric',
													 pattern: '[0-9]*'
												 }
											 }}
											 required/>
					</Stack>
					{/* ------------- POPULAR ------------- */}
					<Stack direction={"column"} spacing={1}>
						<FormControlLabel control={<Checkbox checked={popular} onChange={e => setPopular(e.target.checked)}/>}
															label={"Mark as \"Popular Plan\""}/>
					</Stack>
					{/* ------------- HIDE PLAN ------------- */}
					<Stack direction={"column"} spacing={1}>
						<FormControlLabel control={<Checkbox checked={hidePlan} onChange={e => setHidePlan(e.target.checked)}/>}
															label={"Hide this plan card"}/>
					</Stack>

					<Button variant={"contained"}
									startIcon={<Publish/>}
									disabled={createPlanMutation.isPending}
									sx={{
										maxWidth: "200px"
									}}
									onClick={createPlanHandler}>
						{createPlanMutation.isPending ? "Setting Up..." : "Create Plan"}
					</Button>
				</Stack>
			</Stack>
		)

	} else {
		// Ideally it should not reach here.
		return <></>
	}
}
