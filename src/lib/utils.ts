/**
 * Converts seconds to a human-readable format of time remaining
 * @param seconds The number of seconds to convert
 * @returns A string in the format "X days and Y hours remaining" or "X minutes remaining" for times less than an hour
 */
export function formatSecondsToReadableTime(seconds: number): string {
  // Handle negative values
  if (seconds < 0) {
    return "Invalid input: seconds cannot be negative";
  }

  // Define conversion constants
  const secondsInMinute = 60;
  const secondsInHour = 3600;
  const secondsInDay = 24 * secondsInHour;

  // Calculate days, hours, and minutes
  const days = Math.floor(seconds / secondsInDay);
  const remainingSecondsAfterDays = seconds % secondsInDay;
  const hours = Math.floor(remainingSecondsAfterDays / secondsInHour);
  const minutes = Math.floor((seconds % secondsInHour) / secondsInMinute);

  // Build the result string
  let result = "";

  // If less than an hour, show minutes
  if (days === 0 && hours === 0) {
    return `${minutes} min remaining`;
  }

  if (days > 0) {
    result += `${days} day${days === 1 ? "" : "s"}`;

    if (hours > 0) {
      result += ` ${hours} hr${hours === 1 ? "" : "s"}`;
    }
  } else {
    // Only hours, no days
    result += `${hours} hr${hours === 1 ? "" : "s"}`;
  }

  result += " remaining";

  return result;
}
