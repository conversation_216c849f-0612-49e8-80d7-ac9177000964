import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import routes from "@routes";
import {createBrowserRouter, RouterProvider} from 'react-router-dom';

import '@fontsource/roboto/300.css';
import '@fontsource/roboto/400.css';
import '@fontsource/roboto/500.css';
import '@fontsource/roboto/700.css';
import {QueryClient, QueryClientProvider} from '@tanstack/react-query';
import {ClientSideRowModelModule, QuickFilterModule, PaginationModule, ModuleRegistry} from "ag-grid-community";

// Register all Community features for AG Grid Datatable.
ModuleRegistry.registerModules([ClientSideRowModelModule, PaginationModule, QuickFilterModule]);


// Set up ReactRouter and TanStack Query
const router = createBrowserRouter(routes);
const queryClient = new QueryClient();

const root = ReactDOM.createRoot(
	document.getElementById('root') as HTMLElement
);
root.render(
	<React.StrictMode>
		<QueryClientProvider client={queryClient}>
			<RouterProvider router={router}/>
		</QueryClientProvider>
	</React.StrictMode>
);
