import {Navigate, redirect, RouteObject} from "react-router-dom";
import ErrorPage from "@pages/ErrorPage";
import Base from "@pages/Base";
import Dashboard from "@pages/Dashboard";
import Campaigns from "@pages/Campaigns/Campaigns";
import EmailAccounts from "@pages/EmailAccounts/EmailAccounts";
import AuthBase from "@pages/AuthBase";
import Login from "@pages/Login";
import Signup from "@pages/Signup";
import {clearTokens, retrieveTokens} from "@lib/jwt";
import axios from "axios";
import ConnectExistingDomain from "@pages/EmailAccounts/ConnectExistingDomain";
import ConnectDomainOptions from "@pages/EmailAccounts/ConnectDomainOptions";
import CreateCampaign from "@pages/Campaigns/CreateCampaign";
import CampaignSetup from "@pages/Campaigns/CampaignSetup";
import CampaignDetails from "@pages/Campaigns/CampaignDetails";
import UnsubscribeEmail from "@pages/UnsubscribeEmail";
import Integration from "@pages/Integrations/Integration";
import GoogleAuthCallback from "@pages/Integrations/GoogleAuthCallback";
import Postmaster from "@pages/Integrations/Postmaster";
import ForgotPassword from "@pages/ForgotPassword";
import ChangePassword from "@pages/ChangePassword";
import SettingsPage from "@pages/SettingsPage";
import AdminBase from "@admin/AdminBase";
import AdminDashboard from "@admin/AdminDashboard";
import AdminSpamWords from "@admin/AdminSpamWords";
import AdminSalutations from "@admin/AdminSalutations";
import AdminUsers from "@admin/AdminUsers";
import Emails from "@pages/EmailAccounts/Emails";
import AdminUserDetails from "@admin/AdminUserDetails";
import AdminUserWorkspaces from "@admin/AdminUserWorkspaces";
import AdminUserConnectedDomains from "@admin/AdminUserConnectedDomains";
import AdminUserEmails from "@admin/AdminUserEmails";
import AdminUserCampaigns from "@admin/AdminUserCampaigns";
import AdminUserCampaignDetails from "@admin/AdminUserCampaignDetails";
import FAQPage from "@pages/FAQPage";
import ManageSubscription from "@pages/ManageSubscription";
import AdminSubscriptionPlans from "@admin/AdminSubscriptionPlans";
import AdminCreateSubscriptionPlan from "@admin/AdminCreateSubscriptionPlan";
import AdminEditSubscriptionPlan from "@admin/AdminEditSubscriptionPlan";
import CheckoutSuccess from "@pages/CheckoutSuccess";
import PlanUpdateSuccess from "@pages/PlanUpdateSuccess";
import OnboardingBase from "@pages/OnboardingBase";
import SignupPlanSelection from "@pages/SignupPlanSelection";
import AdminApprovalRequest from "@admin/AdminApprovalRequest";
import CreditHistory from "@pages/CreditHistory";
import AdminUpdate from "@admin/AdminUpdate";
import Updates from "@pages/Updates";
import AccountEmailVerification from "@pages/AccountEmailVerification"
import ContactLists from "@pages/ContactLists";
import ContactListDetails from "@pages/ContactListDetails";
import AdminAllCampaign from "@admin/AdminAllCampaign";
import IntegrationPage from "@pages/Integrations/IntegrationPage";

export const urls = {
	// Admin
	"adminDashboard": "/admin/dashboard",
	"adminAllUsers": "/admin/users",
	"adminUserDetails": "/admin/users/:userId",
	"adminUserWorkspaces": "/admin/users/:userId/workspaces",
	"adminUserConnectedDomains": "/admin/users/:userId/connected-domains",
	"adminUserEmails": "/admin/users/:userId/emails",
	"adminUserCampaigns": "/admin/users/:userId/campaigns",
	"adminUserCampaignDetails": "/admin/users/:userId/campaigns/:campaignUID",
	"adminSpamWords": "/admin/spam-words",
	"adminSalutationWords": "/admin/salutation-words",
	"adminSubscriptionPlans": "/admin/subscription-plans",
	"adminCreateSubscriptionPlans": "/admin/subscription-plans/create",
	"adminEditSubscriptionPlan": "/admin/subscription-plan/edit/:planId",
	"adminEmailContentApproval": "/admin/email-content-approval",
	"adminUpdate" : "/admin/update",
	"adminAllCampaign": "/admin/all-campaign",

	// User
	"login": "/auth/login",
	"signup": "/auth/signup",
	"signupPlanSelection": "/onboarding/plan-selection",
	"resetPassword": "/auth/reset-password",
	"changePassword": "/auth/change-password",
	"logout": "/logout",
	"accountEmailVerification": "/auth/verify-account-email/:token",

	"dashboard": "/dashboard",
	"settings": "/settings",
	"faq": "/faq",
	"manageSubscription": "/manage-subscription",
	"checkoutSuccess": "/checkout/success",
	"planUpdateSuccess": "/plan-update/success",

	"emailAccounts": "/email-accounts",
	"emails": "/email-accounts/emails/:domain",
	"connectDomainOptions": "/email-accounts/connect",
	"connectNewDomain": "/email-accounts/connect/existing",

	"contactLists": "/contact-lists",
	"contactListDetails": "/contact-lists/:contactListUID",

	"campaigns": "/campaigns",
	"createCampaign": "/campaigns/create",
	"campaignSetup": "/campaigns/:campaignUID/setup/",
	"campaignEmailDetails": "/campaigns/:campaignUID/",

	"integrations": "/integrations/",
	"googlePostmaster": "/integrations/google-postmaster/",
	"integrationPage": "/integrations-page/",

	"googleAuthCallback": "/google-auth/callback/",

	"unsubscribe": "/unsubscribe/",

	"SendingHistory" : "/sending-history/",

	"Updates" : "/updates/"

}

const routes: RouteObject[] = [
	{
		path: "/auth",
		element: <AuthBase/>,
		id: "authBase",
		errorElement: <ErrorPage/>,
		children: [
			{
				index: true,
				element: <Navigate to={urls["login"]} replace={true}/>
			},
			{
				path: urls["login"],
				element: <Login/>,
			},
			{
				path: urls["signup"],
				element: <Signup/>,
			},
			{
				path: urls["resetPassword"],
				element: <ForgotPassword/>,
			},
			{
				path: urls["changePassword"],
				element: <ChangePassword/>,
			},
			{
				path: urls['accountEmailVerification'],
				element: <AccountEmailVerification />
			},
		]
	},
	{
		path: "/onboarding",
		element: <OnboardingBase/>,
		id: "onboarding",
		errorElement: <ErrorPage/>,
		children: [
			{
				path: urls["signupPlanSelection"],
				element: <SignupPlanSelection/>,
			}
		]
	},
	{
		path: urls["logout"],
		loader: async () => {
			return await logoutUser();
		}
	},
	{
		path: "/admin",
		element: <AdminBase/>,
		id: "adminbase",
		children: [
			{
				index: true,
				element: <Navigate to={urls["adminDashboard"]} replace={true}/>
			},
			{
				path: urls["adminDashboard"],
				element: <AdminDashboard/>,
			},
			{
				path: urls["adminAllUsers"],
				element: <AdminUsers/>,
			},
			{
				path: urls["adminUserDetails"],
				element: <AdminUserDetails/>,
			},
			{
				path: urls["adminUserWorkspaces"],
				element: <AdminUserWorkspaces/>,
			},
			{
				path: urls["adminUserConnectedDomains"],
				element: <AdminUserConnectedDomains/>,
			},
			{
				path: urls["adminUserEmails"],
				element: <AdminUserEmails/>,
			},
			{
				path: urls["adminUserCampaigns"],
				element: <AdminUserCampaigns/>,
			},
			{
				path: urls["adminUserCampaignDetails"],
				element: <AdminUserCampaignDetails/>,
			},
			{
				path: urls["adminSpamWords"],
				element: <AdminSpamWords/>,
			},
			{
				path: urls["adminSalutationWords"],
				element: <AdminSalutations/>,
			},
			{
				path: urls["adminSubscriptionPlans"],
				element: <AdminSubscriptionPlans/>,
			},
			{
				path: urls["adminCreateSubscriptionPlans"],
				element: <AdminCreateSubscriptionPlan/>,
			},
			{
				path: urls["adminEditSubscriptionPlan"],
				element: <AdminEditSubscriptionPlan/>,
			},
			{
				path: urls["adminEmailContentApproval"],
				element: <AdminApprovalRequest/>,
			},
			{
				path: urls["adminUpdate"],
				element: <AdminUpdate/>,
			},
			{
				path: urls["adminAllCampaign"],
				element: <AdminAllCampaign/>,
			},
		]
	},
	{
		path: "/",
		element: <Base/>,
		id: "base",
		errorElement: <ErrorPage/>,
		children: [
			{
				index: true,
				element: <Navigate to={urls["dashboard"]} replace={true}/>
			},
			{
				path: urls["dashboard"],
				element: <Dashboard/>,
			},
			{
				path: urls["settings"],
				element: <SettingsPage/>,
			},
			{
				path: urls["faq"],
				element: <FAQPage/>,
			},
			{
				path: urls["manageSubscription"],
				element: <ManageSubscription/>,
			},
			{
				path: urls["checkoutSuccess"],
				element: <CheckoutSuccess/>,
			},
			{
				path: urls["planUpdateSuccess"],
				element: <PlanUpdateSuccess/>,
			},
			{
				path: urls["emailAccounts"],
				element: <EmailAccounts/>,
			},
			{
				path: urls["emails"],
				element: <Emails/>,
			},
			{
				path: urls["connectNewDomain"],
				element: <ConnectExistingDomain/>,
			},
			{
				path: urls["connectDomainOptions"],
				element: <ConnectDomainOptions/>,
			},
			{
				path: urls["campaigns"],
				element: <Campaigns/>,
			},
			{
				path: urls["createCampaign"],
				element: <CreateCampaign/>,
			},
			{
				path: urls["campaignSetup"],
				element: <CampaignSetup/>,
			},
			{
				path: urls["campaignEmailDetails"],
				element: <CampaignDetails/>,
			},
			{
				path: urls["contactLists"],
				element: <ContactLists/>,
			},
			{
				path: urls["contactListDetails"],
				element: <ContactListDetails/>,
			},
			{
				path: urls["integrations"],
				element: <Integration/>,
			},
			{
				path: urls["googlePostmaster"],
				element: <Postmaster/>,
			},
			{
				path: urls["googleAuthCallback"],
				element: <GoogleAuthCallback/>,
			},
			{
				path: urls["SendingHistory"],
				element: <CreditHistory/>
			},
			{
				path: urls["Updates"],
				element: <Updates/>
			},
			{
				path: urls["integrationPage"],
				element: <IntegrationPage/>,
			},

		]
	},
	{
		path: "/unsubscribe",
		element: <UnsubscribeEmail/>,
		id: "unsubscribe",
		errorElement: <ErrorPage/>,
	},
]

async function logoutUser() {
	const tokens = retrieveTokens();

	// If refresh token is already missing, clear out access token (if present) and redirect to login page.
	if (!tokens.refresh) {
		clearTokens();
		return redirect(urls["login"]);
	}

	// Send request to logout endpoint to blacklist current tokens
	try {
		await axios({
			method: "post",
			url: process.env.REACT_APP_BACKEND + "/auth/logout/",
			data: {
				refresh: tokens.refresh
			},
			responseType: "json"
		});
	} catch (err) {
		// Logout failed due to client or server error. Return to dashboard page
		// Should not ideally happen.
		console.error(err);
		return redirect(urls['dashboard'])
	}

	// Delete tokens and redirect to login page
	clearTokens();
	return redirect(urls['login']);
}

export default routes;
