import {ChangeEvent, CSSProperties, useEffect, useState} from "react";
import {Alert, Box, Checkbox, FormControlLabel, FormGroup, Stack, Typography} from "@mui/material";
import {Link} from "react-router-dom";
import {urls} from "@routes";

export interface CDSManagedSubdomain {
	id: string
	domain: string
	checked: boolean
}

export default function CampaignDomainSelection(props: {
	items: CDSManagedSubdomain[],
	onChange: (selectedItems: CDSManagedSubdomain[]) => void,
	style?: CSSProperties,
}) {
	// Initialize state to track checked status of each item
	const [
		checkedState,
		setCheckedState
	] = useState<CDSManagedSubdomain[]>([]);

	// Initialize the checked state when items change
	useEffect(() => {
		setCheckedState(props.items);
	}, [props.items]);

	// Handle individual checkbox changes
	const handleCheckboxChange = (event: ChangeEvent<HTMLInputElement>) => {
		const {name, checked} = event.target;

		// Update the subdomain's "checked" value.
		let updatedValue = checkedState.map(item => {
			return item.id === name ? {...item, checked: checked} : item
		});
		setCheckedState(updatedValue);

		props.onChange(updatedValue);
	};

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<Stack direction={"column"} spacing={1} sx={props.style}>
			<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
				<Box component="span" sx={{color: "error.main"}}>*</Box>&nbsp;Select domains to use for Campaign:
			</Typography>
			<Alert variant={"standard"} severity={"warning"} sx={{maxWidth: "fit-content"}}>
				Email sending domains cannot be changed once campaign has been started.
			</Alert>
			<FormGroup>
				{/* if no domains are available */}
				{checkedState.length === 0 &&
            <Alert severity={"error"} sx={{maxWidth: "fit-content"}}>
                No domains are available. We need at least one domain/subdomain to run the campaign.&nbsp;
                <Link to={urls["emailAccounts"]}>Add a Domain.</Link>
            </Alert>}
				{/* if 1 or more domains are available */}
				{checkedState.map((item) => {
					return (
						<FormControlLabel
							key={item.id}
							control={
								<Checkbox
									key={item.id}
									checked={item.checked}
									onChange={handleCheckboxChange}
									name={item.id}
								/>
							}
							sx={{maxWidth: "fit-content"}}
							label={item.domain}
						/>
					);
				})}
			</FormGroup>
		</Stack>
	);
}
