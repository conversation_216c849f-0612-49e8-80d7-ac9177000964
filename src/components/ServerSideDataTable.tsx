import {AgGridReact} from 'ag-grid-react';
import {useEffect, useState} from 'react';
import {ColDef, RowSelectionModule} from "ag-grid-community";
import {useTheme} from '@mui/material/styles';
import {Box, Stack, TextField} from "@mui/material";
import type { RowSelectionOptions } from "ag-grid-community";
import { ModuleRegistry, TooltipModule } from "ag-grid-community";
import PageLoading from "./PageLoading";

// Register the RowSelectionModule
ModuleRegistry.registerModules([RowSelectionModule, TooltipModule]);

export default function ServerSideDataTable(props: {
	columns: ColDef[],
	rows: any[],
	actions?: JSX.Element,
	noRowsText?: string,
	onSelectionChange?: (selected: any[]) => void
	rowSelection?: RowSelectionOptions,
	loading?: boolean,
	getRowHeight?: (params: any) => number,
}) {
	const muiTheme = useTheme();
	const pageSizes = [10, 50, 100];

	// For switching themes.
	const [gridTheme, setGridTheme] = useState('ag-theme-alpine');

	// For filtering rows.
	const [filterText, setFilterText] = useState("");

	const defaultColDef = {
		flex: 1,
	};

	// Update AG Grid theme when MUI theme changes
	useEffect(() => {
		setGridTheme(muiTheme.palette.mode === 'dark'
			? 'ag-theme-alpine-dark'
			: 'ag-theme-alpine');
	}, [muiTheme.palette.mode]);


	return (
		<Box
			className={gridTheme}
			sx={{
				height: "100%",
				width: '100%',
				'&.ag-theme-alpine': {
					'--ag-foreground-color': muiTheme.palette.text.primary,
					'--ag-background-color': muiTheme.palette.background.paper,
					'--ag-header-foreground-color': muiTheme.palette.text.primary,
					'--ag-header-background-color': muiTheme.palette.background.default,
					'--ag-row-hover-color': muiTheme.palette.action.hover,
				},
				'&.ag-theme-alpine-dark': {
					'--ag-foreground-color': muiTheme.palette.text.primary,
					'--ag-background-color': muiTheme.palette.background.paper,
					'--ag-header-foreground-color': muiTheme.palette.text.primary,
					'--ag-header-background-color': muiTheme.palette.background.default,
					'--ag-row-hover-color': muiTheme.palette.action.hover,
				}
			}}
		>
			<Stack direction={"column"} spacing={2} sx={{height: "100%"}}>
				<Box display={"flex"} flexDirection={"row"} justifyContent={"space-between"}>
					<TextField placeholder={"Search in table..."}
										 value={filterText}
										 size={"small"}
										 sx={{width: "300px"}}
										 onChange={e => setFilterText(e.target.value)}/>
					{props.actions}
				</Box>
				<AgGridReact
					loading={props.loading ?? false}
					rowData={props.rows}
					columnDefs={props.columns}
					defaultColDef={defaultColDef}
					tooltipShowDelay={0}
					tooltipHideDelay={2000}
					pagination={true}
					paginationPageSize={pageSizes[1]}
					paginationPageSizeSelector={pageSizes}
					quickFilterText={filterText}
					enableCellTextSelection={true}
					ensureDomOrder={true}
					overlayNoRowsTemplate={props.noRowsText || "No Rows Available"}
					loadingOverlayComponent={PageLoading}
					rowSelection={props.rowSelection}
					onSelectionChanged={(event) => {
						const selectedRows = event.api.getSelectedRows();
						props.onSelectionChange?.(selectedRows);
					}}
					getRowHeight={props.getRowHeight}
				/>
			</Stack>
		</Box>
	)
}
