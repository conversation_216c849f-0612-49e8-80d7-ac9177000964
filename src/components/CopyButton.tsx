import {Tooltip} from "@mui/material";
import IconButton from "@mui/material/IconButton";
import {ContentCopy} from "@mui/icons-material";
import {useState} from "react";

export default function CopyButton(props: {
	text: string
}) {
	const [copied, setCopied] = useState(false);

	const handleCopy = () => {
		navigator.clipboard.writeText(props.text).then(() => {
			setCopied(true);
			// Reset copied state after 2 seconds
			setTimeout(() => setCopied(false), 2000);
		});
	};

	return (
		<Tooltip title={copied ? "Copied!" : "Copy to clipboard"} arrow>
			<IconButton onClick={handleCopy} size={"small"} color={copied ? "success" : "default"}>
				<ContentCopy fontSize={"small"}/>
			</IconButton>
		</Tooltip>
	)
}
