import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField,
    Checkbox,
    FormControlLabel,
    Stack,
    Divider,
    Alert
} from '@mui/material';

export interface FieldMapping {
    csvColumn: string;
    mappedField: string;
    customFieldName?: string;
}

export interface FieldMappingProps {
    csvHeaders: string[];
    initialMappings?: FieldMapping[];
    hasUnsubscribeColumn: boolean;
    unsubscribeColumnMapping?: string;
    onMappingsChange: (mappings: FieldMapping[]) => void;
    onUnsubscribeChange: (hasUnsubscribe: boolean, columnMapping?: string) => void;
}

// Predefined field options
const PREDEFINED_FIELDS = [
    { value: 'First Name', label: 'First Name' },
    { value: 'Last Name', label: 'Last Name' },
    { value: 'Email Address', label: 'Email Address' },
    { value: 'Phone Number', label: 'Phone Number' },
    { value: 'Company Name', label: 'Company Name' },
    { value: 'Website', label: 'Website' },
    { value: 'LinkedIn Profile', label: 'LinkedIn Profile' },
    { value: 'Location', label: 'Location' },
    { value: 'Custom Field', label: 'Custom Field' },
    { value: 'Ignore Field', label: 'Ignore Field' }
];

// Auto-matching patterns for CSV headers
const AUTO_MATCH_PATTERNS: Record<string, string[]> = {
    'First Name': ['first name', 'fname', 'f name', 'firstname', 'given name'],
    'Last Name': ['last name', 'lname', 'l name', 'lastname', 'surname', 'family name'],
    'Email Address': ['email', 'email address', 'e-mail', 'mail', 'email_address'],
    'Phone Number': ['phone', 'phone number', 'mobile', 'cell', 'telephone', 'tel'],
    'Company Name': ['company', 'company name', 'organization', 'org', 'business'],
    'Website': ['website', 'web site', 'url', 'site', 'homepage'],
    'LinkedIn Profile': ['linkedin', 'linkedin profile', 'linkedin url', 'li profile'],
    'Location': ['location', 'address', 'city', 'country', 'region', 'place']
};



export default function FieldMapping({
    csvHeaders,
    initialMappings = [],
    hasUnsubscribeColumn,
    unsubscribeColumnMapping,
    onMappingsChange,
    onUnsubscribeChange
}: FieldMappingProps) {
    const [mappings, setMappings] = useState<FieldMapping[]>(initialMappings);
    const [unsubscribeEnabled, setUnsubscribeEnabled] = useState(hasUnsubscribeColumn);
    const [unsubscribeColumn, setUnsubscribeColumn] = useState(unsubscribeColumnMapping || '');

    // Auto-match function
    const autoMatchField = (header: string): string => {
        const headerLower = header.toLowerCase().trim();

        // Find matching predefined field
        for (const [fieldKey, patterns] of Object.entries(AUTO_MATCH_PATTERNS)) {
            if (patterns.some(pattern => headerLower.includes(pattern))) {
                return fieldKey;
            }
        }

        return 'Ignore Field';
    };

    // Initialize mappings for all CSV headers if not provided
    useEffect(() => {
        if (initialMappings.length === 0 && csvHeaders.length > 0) {
            const newMappings = csvHeaders.map(header => ({
                csvColumn: header,
                mappedField: autoMatchField(header)
            }));
            if (unsubscribeColumnMapping) {
                const index = newMappings.findIndex(m => m.csvColumn === unsubscribeColumnMapping);
                if (index !== -1) {
                    newMappings[index].mappedField = 'Ignore Field';
                }
            }
            setMappings(newMappings);
            onMappingsChange(newMappings);
        }
    }, [csvHeaders, initialMappings, unsubscribeColumnMapping]);



    const handleMappingChange = (csvColumn: string, field: keyof FieldMapping, value: string) => {
        const newMappings = [...mappings];
        const index = newMappings.findIndex(m => m.csvColumn === csvColumn);
        if (index === -1) return;
        newMappings[index] = { ...newMappings[index], [field]: value };

        // Clear custom field name if not custom field
        if (field === 'mappedField' && value !== 'Custom Field') {
            newMappings[index].customFieldName = undefined;
        }

        setMappings(newMappings);
        onMappingsChange(newMappings);
    };

    const handleUnsubscribeToggle = (checked: boolean) => {
        setUnsubscribeEnabled(checked);
        if (!checked) {
            const prevColumn = unsubscribeColumn;
            setUnsubscribeColumn('');
            onUnsubscribeChange(false);
            // Reset the previous unsubscribe column to auto-match
            if (prevColumn) {
                const newMappings = [...mappings];
                const index = newMappings.findIndex(m => m.csvColumn === prevColumn);
                if (index !== -1) {
                    newMappings[index].mappedField = autoMatchField(prevColumn);
                    setMappings(newMappings);
                    onMappingsChange(newMappings);
                }
            }
        } else {
            onUnsubscribeChange(true, unsubscribeColumn);
        }
    };

    const handleUnsubscribeColumnChange = (column: string) => {
        const prevColumn = unsubscribeColumn;
        setUnsubscribeColumn(column);
        onUnsubscribeChange(unsubscribeEnabled, column);
        // Set new column to Ignore Field
        const newMappings = [...mappings];
        const newIndex = newMappings.findIndex(m => m.csvColumn === column);
        if (newIndex !== -1) {
            newMappings[newIndex].mappedField = 'Ignore Field';
        }
        // Reset previous column to auto-match if different
        if (prevColumn && prevColumn !== column) {
            const prevIndex = newMappings.findIndex(m => m.csvColumn === prevColumn);
            if (prevIndex !== -1) {
                newMappings[prevIndex].mappedField = autoMatchField(prevColumn);
            }
        }
        setMappings(newMappings);
        onMappingsChange(newMappings);
    };

    return (
        <Box sx={{ mt: 3 }}>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                Field Mapping
            </Typography>

            <Alert severity="info" sx={{ mb: 3 }}>
                Map your CSV columns to the variables you want to add it on the campaign.
            </Alert>

            {/* Unsubscribe Column Section */}
            <Box sx={{ mb: 3, p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                <FormControlLabel
                    control={
                        <Checkbox
                            checked={unsubscribeEnabled}
                            onChange={(e) => handleUnsubscribeToggle(e.target.checked)}
                        />
                    }
                    label="Does this list have an unsubscribe column?"
                />

                {unsubscribeEnabled && (
                    <FormControl fullWidth sx={{ mt: 2 }}>
                        <InputLabel>Unsubscribe Column</InputLabel>
                        <Select
                            value={unsubscribeColumn}
                            label="Unsubscribe Column"
                            onChange={(e) => handleUnsubscribeColumnChange(e.target.value)}
                        >
                            {csvHeaders.map((header) => (
                                <MenuItem key={header} value={header}>
                                    {header}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                )}
            </Box>

            <Divider sx={{ mb: 3 }} />

            {/* Field Mappings */}
            <Stack spacing={2}>
                {mappings.filter(m => m.csvColumn !== unsubscribeColumn).map((mapping) => (
                    <Box key={mapping.csvColumn} sx={{ p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                        <Stack direction={mapping.mappedField === 'Custom Field' ? 'column' : 'row'} spacing={2} alignItems={mapping.mappedField === 'Custom Field' ? 'flex-start' : 'center'}>
                             <Stack direction="row" spacing={2} alignItems="center" sx={{ width: '100%' }}>
                                 <FormControl sx={{ flex: 1 }}>
                                     <InputLabel>Map to Field</InputLabel>
                                     <Select
                                         value={mapping.mappedField}
                                         label="Map to Field"
                                         onChange={(e) => handleMappingChange(mapping.csvColumn, 'mappedField', e.target.value)}
                                     >
                                         {PREDEFINED_FIELDS.map((field) => (
                                             <MenuItem key={field.value} value={field.value}>
                                                 {field.label}
                                             </MenuItem>
                                         ))}
                                     </Select>
                                 </FormControl>

                                 <Typography variant="h6" color="text.secondary">
                                     →
                                 </Typography>

                                 <Box sx={{ flex: 1 }}>
                                     <Typography variant="body2" color="text.secondary" gutterBottom>
                                         CSV Column
                                     </Typography>
                                     <Typography variant="body1" fontWeight="medium">
                                         {mapping.csvColumn}
                                     </Typography>
                                 </Box>
                             </Stack>

                            {mapping.mappedField === 'Custom Field' && (
                                <TextField
                                    label="Custom Field Name"
                                    value={mapping.customFieldName || ''}
                                    onChange={(e) => handleMappingChange(mapping.csvColumn, 'customFieldName', e.target.value)}
                                    sx={{ width: '100%' }}
                                    required
                                />
                            )}
                        </Stack>
                    </Box>
                ))}
            </Stack>
        </Box>
    );
}
