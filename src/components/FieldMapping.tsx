import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField,
    Checkbox,
    FormControlLabel,
    Stack,
    Divider,
    Alert,
    Button,
    IconButton
} from '@mui/material';
import { Add, Delete } from '@mui/icons-material';

export interface FieldMappingType {
    csvColumn: string;
    mappedField: string;
    customFieldName?: string;
}

export interface FieldMappingProps {
    csvHeaders: string[];
    csvData?: string[][];
    initialMappings?: FieldMappingType[];
    hasUnsubscribeColumn: boolean;
    unsubscribeColumnMapping?: string;
    onMappingsChange: (mappings: FieldMappingType[]) => void;
    onUnsubscribeChange: (hasUnsubscribe: boolean, columnMapping?: string) => void;
}

// Predefined field options (removed "Ignore Field")
const PREDEFINED_FIELDS = [
    { value: 'First Name', label: 'First Name' },
    { value: 'Last Name', label: 'Last Name' },
    { value: 'Email Address', label: 'Email Address' },
    { value: 'Phone Number', label: 'Phone Number' },
    { value: 'Company Name', label: 'Company Name' },
    { value: 'Website', label: 'Website' },
    { value: 'LinkedIn Profile', label: 'LinkedIn Profile' },
    { value: 'Location', label: 'Location' },
    { value: 'Custom Field', label: 'Custom Field' }
];

// Auto-matching patterns for CSV headers
const AUTO_MATCH_PATTERNS: Record<string, string[]> = {
    'First Name': ['first name', 'fname', 'f name', 'firstname', 'given name'],
    'Last Name': ['last name', 'lname', 'l name', 'lastname', 'surname', 'family name'],
    'Email Address': ['email', 'email address', 'e-mail', 'mail', 'email_address'],
    'Phone Number': ['phone', 'phone number', 'mobile', 'cell', 'telephone', 'tel'],
    'Company Name': ['company', 'company name', 'organization', 'org', 'business'],
    'Website': ['website', 'web site', 'url', 'site', 'homepage'],
    'LinkedIn Profile': ['linkedin', 'linkedin profile', 'linkedin url', 'li profile'],
    'Location': ['location', 'address', 'city', 'country', 'region', 'place']
};



export default function FieldMapping({
    csvHeaders,
    csvData = [],
    initialMappings = [],
    hasUnsubscribeColumn,
    unsubscribeColumnMapping,
    onMappingsChange,
    onUnsubscribeChange
}: FieldMappingProps) {
    const [mappings, setMappings] = useState<FieldMappingType[]>(initialMappings);
    const [unsubscribeEnabled, setUnsubscribeEnabled] = useState(hasUnsubscribeColumn);
    const [unsubscribeColumn, setUnsubscribeColumn] = useState(unsubscribeColumnMapping || '');
    const [additionalMappings, setAdditionalMappings] = useState<FieldMappingType[]>([]);

    // Auto-match function
    const autoMatchField = (header: string): string => {
        const headerLower = header.toLowerCase().trim();

        // Find matching predefined field
        for (const [fieldKey, patterns] of Object.entries(AUTO_MATCH_PATTERNS)) {
            if (patterns.some(pattern => headerLower.includes(pattern))) {
                return fieldKey;
            }
        }

        return '';
    };

    // Initialize mappings for auto-matched CSV headers if not provided
    useEffect(() => {
        if (initialMappings.length === 0 && csvHeaders.length > 0) {
            const autoMatchedMappings: FieldMappingType[] = [];

            csvHeaders.forEach(header => {
                const matchedField = autoMatchField(header);
                if (matchedField && !autoMatchedMappings.some(m => m.mappedField === matchedField)) {
                    autoMatchedMappings.push({
                        csvColumn: header,
                        mappedField: matchedField
                    });
                }
            });

            setMappings(autoMatchedMappings);
            onMappingsChange(autoMatchedMappings);
        }
    }, [csvHeaders, initialMappings, onMappingsChange]);

    // Get all currently mapped fields to prevent duplicates
    const getMappedFields = () => {
        const allMappings = [...mappings, ...additionalMappings];
        return allMappings.map(m => m.mappedField).filter(field => field && field !== 'Custom Field');
    };

    // Get available CSV headers for dropdown (excluding already mapped and unsubscribe column)
    const getAvailableCsvHeaders = () => {
        const allMappings = [...mappings, ...additionalMappings];
        const mappedColumns = allMappings.map(m => m.csvColumn);
        return csvHeaders.filter(header =>
            !mappedColumns.includes(header) &&
            header !== unsubscribeColumn
        );
    };

    // Get available predefined fields (excluding already mapped ones)
    const getAvailablePredefinedFields = () => {
        const mappedFields = getMappedFields();
        return PREDEFINED_FIELDS.filter(field => !mappedFields.includes(field.value));
    };

    // Get preview value from CSV data for a specific column
    const getPreviewValue = (csvColumn: string): string => {
        if (!csvData || csvData.length === 0) return '';

        const columnIndex = csvHeaders.indexOf(csvColumn);
        if (columnIndex === -1) return '';

        // Try to get the second row (index 1), fallback to first row (index 0)
        const rowIndex = csvData.length > 1 ? 1 : 0;
        const value = csvData[rowIndex]?.[columnIndex];

        return value || '';
    };

    const handleMappingChange = (csvColumn: string, field: keyof FieldMappingType, value: string) => {
        const newMappings = [...mappings];
        const newAdditionalMappings = [...additionalMappings];

        // Find in main mappings first
        let index = newMappings.findIndex(m => m.csvColumn === csvColumn);
        let isAdditional = false;

        if (index === -1) {
            // Find in additional mappings
            index = newAdditionalMappings.findIndex(m => m.csvColumn === csvColumn);
            isAdditional = true;
        }

        if (index === -1) return;

        const targetArray = isAdditional ? newAdditionalMappings : newMappings;
        targetArray[index] = { ...targetArray[index], [field]: value };

        // Clear custom field name if not custom field
        if (field === 'mappedField' && value !== 'Custom Field') {
            targetArray[index].customFieldName = undefined;
        }

        setMappings(newMappings);
        setAdditionalMappings(newAdditionalMappings);
        onMappingsChange([...newMappings, ...newAdditionalMappings]);
    };

    const handleAddMapping = () => {
        const availableHeaders = getAvailableCsvHeaders();
        const availableFields = getAvailablePredefinedFields();

        if (availableHeaders.length > 0 && availableFields.length > 0) {
            const newMapping: FieldMappingType = {
                csvColumn: availableHeaders[0],
                mappedField: availableFields[0].value
            };

            const newAdditionalMappings = [...additionalMappings, newMapping];
            setAdditionalMappings(newAdditionalMappings);
            onMappingsChange([...mappings, ...newAdditionalMappings]);
        }
    };

    const handleRemoveMapping = (csvColumn: string) => {
        const newAdditionalMappings = additionalMappings.filter(m => m.csvColumn !== csvColumn);
        setAdditionalMappings(newAdditionalMappings);
        onMappingsChange([...mappings, ...newAdditionalMappings]);
    };

    const handleUnsubscribeToggle = (checked: boolean) => {
        setUnsubscribeEnabled(checked);
        if (!checked) {
            setUnsubscribeColumn('');
            onUnsubscribeChange(false);
        } else {
            onUnsubscribeChange(true, unsubscribeColumn);
        }
    };

    const handleUnsubscribeColumnChange = (column: string) => {
        setUnsubscribeColumn(column);
        onUnsubscribeChange(unsubscribeEnabled, column);
    };

    return (
        <Box sx={{ mt: 3 }}>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                Field Mapping
            </Typography>

            <Alert severity="info" sx={{ mb: 3 }}>
                Map your CSV columns to the variables you want to add it on the campaign.
            </Alert>

            {/* Unsubscribe Column Section */}
            <Box sx={{ mb: 3, p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                <FormControlLabel
                    control={
                        <Checkbox
                            checked={unsubscribeEnabled}
                            onChange={(e) => handleUnsubscribeToggle(e.target.checked)}
                        />
                    }
                    label="Does this list have an unsubscribe column?"
                />

                {unsubscribeEnabled && (
                    <FormControl fullWidth sx={{ mt: 2 }}>
                        <InputLabel>Unsubscribe Column</InputLabel>
                        <Select
                            value={unsubscribeColumn}
                            label="Unsubscribe Column"
                            onChange={(e) => handleUnsubscribeColumnChange(e.target.value)}
                        >
                            {csvHeaders.map((header) => (
                                <MenuItem key={header} value={header}>
                                    {header}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                )}
            </Box>

            <Divider sx={{ mb: 3 }} />

            {/* Auto-matched Field Mappings */}
            <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                Auto-matched Fields
            </Typography>

            <Stack spacing={2} sx={{ mb: 3 }}>
                {mappings.filter(m => m.csvColumn !== unsubscribeColumn).map((mapping) => (
                    <Box key={mapping.csvColumn} sx={{ p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                        <Stack direction={mapping.mappedField === 'Custom Field' ? 'column' : 'row'} spacing={2} alignItems={mapping.mappedField === 'Custom Field' ? 'flex-start' : 'center'}>
                            <Stack direction="row" spacing={2} alignItems="center" sx={{ width: '100%' }}>
                                <Box sx={{ flex: 1 }}>
                                    <Typography variant="body2" color="text.secondary" gutterBottom>
                                        Predefined Field
                                    </Typography>
                                    <Typography variant="body1" fontWeight="medium">
                                        {mapping.mappedField}
                                    </Typography>
                                </Box>

                                <Typography variant="h6" color="text.secondary">
                                    ←
                                </Typography>

                                <Box sx={{ flex: 1 }}>
                                    <FormControl fullWidth>
                                        <InputLabel>CSV Column</InputLabel>
                                        <Select
                                            value={mapping.csvColumn}
                                            label="CSV Column"
                                            onChange={(e) => {
                                                const newCsvColumn = e.target.value;
                                                const newMappings = [...mappings];
                                                const index = newMappings.findIndex(m => m.csvColumn === mapping.csvColumn);
                                                if (index !== -1) {
                                                    newMappings[index].csvColumn = newCsvColumn;
                                                    setMappings(newMappings);
                                                    onMappingsChange([...newMappings, ...additionalMappings]);
                                                }
                                            }}
                                        >
                                            {csvHeaders.filter(header =>
                                                header !== unsubscribeColumn &&
                                                (header === mapping.csvColumn || (!mappings.some(m => m.csvColumn === header) && !additionalMappings.some(m => m.csvColumn === header)))
                                            ).map((header) => (
                                                <MenuItem key={header} value={header}>
                                                    {header}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>

                                    {/* Preview Value */}
                                    {mapping.csvColumn && getPreviewValue(mapping.csvColumn) && (
                                        <Box sx={{ mt: 1, p: 1, bgcolor: 'grey.50', borderRadius: 1, border: '1px solid', borderColor: 'grey.200' }}>
                                            <Typography variant="caption" color="text.secondary" display="block">
                                                Preview:
                                            </Typography>
                                            <Typography variant="body2" sx={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>
                                                {getPreviewValue(mapping.csvColumn)}
                                            </Typography>
                                        </Box>
                                    )}
                                </Box>
                            </Stack>

                            {mapping.mappedField === 'Custom Field' && (
                                <TextField
                                    label="Custom Field Name"
                                    value={mapping.customFieldName || ''}
                                    onChange={(e) => handleMappingChange(mapping.csvColumn, 'customFieldName', e.target.value)}
                                    sx={{ width: '100%' }}
                                    required
                                />
                            )}
                        </Stack>
                    </Box>
                ))}
            </Stack>

            {/* Additional Mappings */}
            {additionalMappings.length > 0 && (
                <>
                    <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                        Additional Mappings
                    </Typography>

                    <Stack spacing={2} sx={{ mb: 3 }}>
                        {additionalMappings.map((mapping) => (
                            <Box key={mapping.csvColumn} sx={{ p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                                <Stack direction={mapping.mappedField === 'Custom Field' ? 'column' : 'row'} spacing={2} alignItems={mapping.mappedField === 'Custom Field' ? 'flex-start' : 'center'}>
                                    <Stack direction="row" spacing={2} alignItems="center" sx={{ width: '100%' }}>
                                        <FormControl sx={{ flex: 1 }}>
                                            <InputLabel>Predefined Field</InputLabel>
                                            <Select
                                                value={mapping.mappedField}
                                                label="Predefined Field"
                                                onChange={(e) => handleMappingChange(mapping.csvColumn, 'mappedField', e.target.value)}
                                            >
                                                {getAvailablePredefinedFields().concat(
                                                    PREDEFINED_FIELDS.filter(f => f.value === mapping.mappedField)
                                                ).map((field) => (
                                                    <MenuItem key={field.value} value={field.value}>
                                                        {field.label}
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                        </FormControl>

                                        <Typography variant="h6" color="text.secondary">
                                            ←
                                        </Typography>

                                        <Box sx={{ flex: 1 }}>
                                            <FormControl fullWidth>
                                                <InputLabel>CSV Column</InputLabel>
                                                <Select
                                                    value={mapping.csvColumn}
                                                    label="CSV Column"
                                                    onChange={(e) => {
                                                        const newCsvColumn = e.target.value;
                                                        const newAdditionalMappings = [...additionalMappings];
                                                        const index = newAdditionalMappings.findIndex(m => m.csvColumn === mapping.csvColumn);
                                                        if (index !== -1) {
                                                            newAdditionalMappings[index].csvColumn = newCsvColumn;
                                                            setAdditionalMappings(newAdditionalMappings);
                                                            onMappingsChange([...mappings, ...newAdditionalMappings]);
                                                        }
                                                    }}
                                                >
                                                    {csvHeaders.filter(header =>
                                                        header !== unsubscribeColumn &&
                                                        (header === mapping.csvColumn || (!mappings.some(m => m.csvColumn === header) && !additionalMappings.some(m => m.csvColumn === header)))
                                                    ).map((header) => (
                                                        <MenuItem key={header} value={header}>
                                                            {header}
                                                        </MenuItem>
                                                    ))}
                                                </Select>
                                            </FormControl>

                                            {/* Preview Value */}
                                            {mapping.csvColumn && getPreviewValue(mapping.csvColumn) && (
                                                <Box sx={{ mt: 1, p: 1, bgcolor: 'grey.50', borderRadius: 1, border: '1px solid', borderColor: 'grey.200' }}>
                                                    <Typography variant="caption" color="text.secondary" display="block">
                                                        Preview:
                                                    </Typography>
                                                    <Typography variant="body2" sx={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>
                                                        {getPreviewValue(mapping.csvColumn)}
                                                    </Typography>
                                                </Box>
                                            )}
                                        </Box>

                                        <IconButton
                                            onClick={() => handleRemoveMapping(mapping.csvColumn)}
                                            color="error"
                                            size="small"
                                        >
                                            <Delete />
                                        </IconButton>
                                    </Stack>

                                    {mapping.mappedField === 'Custom Field' && (
                                        <TextField
                                            label="Custom Field Name"
                                            value={mapping.customFieldName || ''}
                                            onChange={(e) => handleMappingChange(mapping.csvColumn, 'customFieldName', e.target.value)}
                                            sx={{ width: '100%' }}
                                            required
                                        />
                                    )}
                                </Stack>
                            </Box>
                        ))}
                    </Stack>
                </>
            )}

            {/* Add More Button */}
            {(getAvailableCsvHeaders().length > 0 && getAvailablePredefinedFields().length > 0) && (
                <Button
                    startIcon={<Add />}
                    onClick={handleAddMapping}
                    variant="outlined"
                    sx={{ mt: 2 }}
                >
                    Add More
                </Button>
            )}
        </Box>
    );
}
