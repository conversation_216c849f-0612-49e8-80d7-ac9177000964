import React, {useRef, useState} from 'react';
import Button from '@mui/material/Button';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import Box from '@mui/material/Box';
import InputAdornment from '@mui/material/InputAdornment';
import SearchIcon from '@mui/icons-material/Search';
import {DataObject} from "@mui/icons-material";

interface Props {
	options: Array<string>
	onClickHandler: (value: string) => void
	maxMenuHeight: number
}

const VariableSelectionMenu = ({options, onClickHandler, maxMenuHeight = 300}: Props) => {
	const [anchorEl, setAnchorEl] = useState(null);
	const [searchTerm, setSearchTerm] = useState('');
	const open = Boolean(anchorEl);
	const searchInputRef = useRef(null); // Ref for the search input

	const handleClick = (event: React.MouseEvent<HTMLElement>) => {
		// @ts-ignore
		setAnchorEl(event.currentTarget);
	};

	const handleClose = () => {
		setAnchorEl(null);
		setSearchTerm(''); // Clear search term on close
	};

	const handleSearchChange = (event) => {
		setSearchTerm(event.target.value);
	};

	const filteredOptions = options.filter(option =>
		option.toLowerCase().includes(searchTerm.toLowerCase())
	);

	return (
		<div>
			<Button
				onClick={handleClick}
				variant={"outlined"}
				size={"small"}
				startIcon={<DataObject/>}
			>
				Variables
			</Button>
			<Menu
				anchorEl={anchorEl}
				open={open}
				onClose={handleClose}
				MenuListProps={{
					// Prevent menu from closing when search input is clicked
					onKeyDown: (e) => {
						if (e.key === 'Escape') {
							handleClose();
						}
					},
					onMouseDown: (e) => {
						// Prevent menu from closing when clicking inside the search input
						// @ts-ignore
						if (searchInputRef.current && searchInputRef.current.contains(e.target)) {
							e.stopPropagation();
						}
					}
				}}
				// Adjust transformOrigin and anchorOrigin to make the menu appear correctly
				anchorOrigin={{
					vertical: 'bottom',
					horizontal: 'left',
				}}
				transformOrigin={{
					vertical: 'top',
					horizontal: 'left',
				}}
			>
				<Box sx={{padding: 1}}>
					<TextField
						ref={searchInputRef} // Assign ref to the TextField
						autoFocus // Automatically focus the search input when menu opens
						fullWidth
						variant="outlined"
						size="small"
						placeholder="Search..."
						value={searchTerm}
						onChange={handleSearchChange}
						slotProps={{
							input: {
								startAdornment: (
									<InputAdornment position="start">
										<SearchIcon/>
									</InputAdornment>
								),
							},
						}}
						// Prevent menu from closing when clicking the TextField
						onClick={(e) => e.stopPropagation()}
						onKeyDown={(e) => {
							if (e.key === 'Escape') {
								handleClose();
							}
							e.stopPropagation(); // Stop propagation for other keys too if needed
						}}
					/>
				</Box>
				<Box sx={{maxHeight: maxMenuHeight, overflowY: 'auto'}}>
					{filteredOptions.length > 0 ? (
						filteredOptions.map((option, index) => (
							<MenuItem key={index} onClick={() => {
								onClickHandler(option);
								handleClose();
							}}>
								{option}
							</MenuItem>
						))
					) : (
						<MenuItem disabled>No results found</MenuItem>
					)}
				</Box>
			</Menu>
		</div>
	);
};

export default VariableSelectionMenu;
