import { useState, useEffect } from "react";
import { Box, Button, useTheme, IconButton } from "@mui/material";
import { ContactEmergency, Close as CloseIcon } from "@mui/icons-material";
import Cal, { getCalApi } from "@calcom/embed-react";

export default function CalendyButton() {
  const theme = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    (async function () {
      const cal = await getCalApi({ namespace: "15min" });
      cal("ui", { hideEventTypeDetails: false, layout: "month_view" });
    })();
  }, []);

  return (
    <Box sx={{ width: "100%" }}>
      <Button
        onClick={() => setIsOpen(true)}
        sx={{
          width: "100%",
          justifyContent: "left",
          color: theme.palette.text.primary,
          textTransform: "none",
          fontWeight: 500,
          fontSize: "1rem",
        }}
        startIcon={<ContactEmergency />}
      >
        &nbsp;&nbsp;&nbsp;Get Demo
      </Button>

      {isOpen && (
        <Box
          sx={{
            position: "fixed",
            inset: 0,
            bgcolor: "rgba(0,0,0,0.6)",
            zIndex: 1300,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
			      marginTop: "4rem"
          }}
          onClick={() => setIsOpen(false)}
        >
          <IconButton
            onClick={() => setIsOpen(false)}
            sx={{
              position: "absolute",
              top: 8,
              right: 8,
              color: theme.palette.grey[700],
              zIndex: 10,
              backgroundColor: "rgba(255,255,255,0.8)",
              "&:hover": {
              backgroundColor: "rgba(255,255,255,1)",
              },
            }}
          >
				<CloseIcon />
				</IconButton>


            <Cal
              namespace="15min"
              calLink="junaidansari/15min"
              style={{ width: "100%", height: "100%" }}
              config={{ layout: "month_view" }}
            />
          </Box>
        // </Box>
      )}
    </Box>
  );
}