import {But<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>itle, Input, Stack} from "@mui/material";
import {FormatBold} from "@mui/icons-material";
import {useState} from "react";

export default function InsertBoldTextButton(props: {
	onApply: (text: string) => void
}) {
	const [openDialog, setOpenDialog] = useState<boolean>(false);

	return (
		<>
			<Button
				onClick={() => {
					setOpenDialog(true);
				}}
				variant={"outlined"}
				size={"small"}
				startIcon={<FormatBold/>}
			>
				Bold
			</Button>
			<InsertBoldTextDialog open={openDialog}
														close={() => setOpenDialog(false)}
														onApply={props.onApply}/>
		</>
	)
}


function InsertBoldTextDialog(props: {
	open: boolean,
	close: () => void,
	onApply: (text: string) => void,
}) {
	const [text, setText] = useState("");

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<Dialog open={props.open} maxWidth={"xs"} fullWidth>
			<DialogTitle>Add Bold Tag</DialogTitle>
			<DialogContent>
				<Stack direction={"column"} spacing={2}>
					<Input value={text}
								 onChange={e => setText(e.target.value)}
								 placeholder={"Text"}/>
				</Stack>
			</DialogContent>
			<DialogActions>
				<Button onClick={props.close}>Cancel</Button>
				<Button onClick={() => {
					props.onApply(text);
					props.close();
				}}>
					Apply
				</Button>
			</DialogActions>
		</Dialog>
	)
}
