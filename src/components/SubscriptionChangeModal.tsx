import React, {useEffect, useState} from 'react';
import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON>,
	Dialog,
	DialogActions,
	DialogContent,
	DialogTitle,
	Divider,
	Stack,
	Typography
} from '@mui/material';

import {bouncy} from "ldrs";
bouncy.register();


const SubscriptionChangeModal = ({
																	 open,
																	 onClose,
																	 onConfirm,
																	 planName,
																	 price,
																	 billingPeriod,
																 }) => {
	const [
		isPaymentProcessing,
		setIsPaymentProcessing
	] = useState<boolean>(false);

	useEffect(() => {
		setIsPaymentProcessing(false);
	}, [open]);

	function handleConfirmChange() {
		setIsPaymentProcessing(true);
		onConfirm();
	}

	// ==============================================================
	// ---------------------- MAIN RENDER CODE ----------------------
	// ==============================================================

	return (
		<Dialog
			open={open}
			onClose={onClose}
			maxWidth="sm"
			fullWidth
			PaperProps={{
				sx: {borderRadius: 2}
			}}
		>
			{!isPaymentProcessing && <DialogTitle sx={{pb: 1}}>
				<Typography variant="h5" component="h2" fontWeight="600">
					Confirm Plan Change
				</Typography>
			</DialogTitle>}

			{isPaymentProcessing ?
				<IsProcessingDialogContent/> :
				<DefaultDialogContent planName={planName} price={price} billingPeriod={billingPeriod}/>}

			<DialogActions sx={{px: 3, pb: 3, gap: 1}}>
				<Button
					onClick={onClose}
					disabled={isPaymentProcessing}
					variant="outlined"
					color="inherit"
					size="large"
					sx={{minWidth: 100}}
				>
					Cancel
				</Button>
				<Button
					onClick={handleConfirmChange}
					disabled={isPaymentProcessing}
					variant="contained"
					color="primary"
					size="large"
					sx={{minWidth: 100}}
				>
					Confirm Change
				</Button>
			</DialogActions>
		</Dialog>
	);
};

function DefaultDialogContent(props: { planName: any, price: any, billingPeriod: any }) {
	return (
		<DialogContent>
			<Box sx={{mb: 3}}>
				<Typography variant="h6" color="primary" gutterBottom>
					{props.planName}
				</Typography>
				<Typography variant="h4" fontWeight="700" color="text.primary">
					${props.price}
					<Typography component="span" variant="body1" color="text.secondary" sx={{ml: 1}}>
						/ {props.billingPeriod}
					</Typography>
				</Typography>
			</Box>

			<Divider sx={{my: 2}}/>

			<Alert severity="info" sx={{mb: 2}}>
				<Typography variant="body2" fontWeight="500">
					Important billing changes
				</Typography>
			</Alert>

			<Box sx={{pl: 2}}>
				<Typography variant="body2" sx={{mb: 1.5, display: "flex", alignItems: "flex-start"}}>
					<Box component="span" sx={{mr: 1, mt: 0.5, fontSize: "8px"}}>●</Box>
					For upgrades, you will be charged the full amount and receive additional credits immediately upon
					confirmation. Your billing cycle will change to today's date going forward.
				</Typography>
				<Typography variant="body2" sx={{mb: 1.5, display: "flex", alignItems: "flex-start"}}>
					<Box component="span" sx={{mr: 1, mt: 0.5, fontSize: "8px"}}>●</Box>
					For downgrades, you will be switched over to selected plan at the end of current billing cycle.
				</Typography>
			</Box>
		</DialogContent>
	);
}

function IsProcessingDialogContent() {
	return (
		<DialogContent>
			<Stack direction={"column"} spacing={4} justifyContent={"center"} alignItems={"center"}>
				<Stack direction={"column"} spacing={1} justifyContent={"center"} alignItems={"center"}>
					<Typography variant={"h5"} fontWeight={"bold"} align={"center"}>
						Processing Payment...
					</Typography>
					<Typography variant={"body2"} align={"center"}>
						This should only take a moment. Please do not close this tab or exit out of this page.<br/>
						If there's any issue, get in touch with us on live chat.
					</Typography>
				</Stack>
				<l-bouncy size="60" color={"#42a5f5"}></l-bouncy>
			</Stack>
		</DialogContent>
	);
}

export default SubscriptionChangeModal;
