import {But<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content, DialogTitle, Input, Stack} from "@mui/material";
import {InsertLink} from "@mui/icons-material";
import {useState} from "react";

export default function InsertLinkButton(props: {
	onApply: (text: string, link: string) => void
}) {
	const [openDialog, setOpenDialog] = useState<boolean>(false);

	return (
		<>
			<Button
				onClick={() => {
					setOpenDialog(true);
				}}
				variant={"outlined"}
				size={"small"}
				startIcon={<InsertLink/>}
			>
				Link
			</Button>
			<InsertLinkDialog open={openDialog}
												close={() => setOpenDialog(false)}
												onApply={props.onApply}/>
		</>
	)
}


function InsertLinkDialog(props: {
	open: boolean,
	close: () => void,
	onApply: (text: string, link: string) => void,
}) {
	const [text, setText] = useState("");
	const [link, setLink] = useState("");

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<Dialog open={props.open} maxWidth={"xs"} fullWidth>
			<DialogTitle>Add Link</DialogTitle>
			<DialogContent>
				<Stack direction={"column"} spacing={2}>
					<Input value={text}
								 onChange={e => setText(e.target.value)}
								 placeholder={"Text"}/>
					<Input value={link}
								 onChange={e => setLink(e.target.value)}
								 placeholder={"Link"}/>
				</Stack>
			</DialogContent>
			<DialogActions>
				<Button onClick={props.close}>Cancel</Button>
				<Button onClick={() => {
					props.onApply(text, link);
					props.close();
				}}
								disabled={!text.trim() || !link.trim()}>
					Apply
				</Button>
			</DialogActions>
		</Dialog>
	)
}
