import {
	Box,
	darken,
	lighten,
	LinearProgress,
	List,
	ListItem,
	ListItemIcon,
	ListItemText,
	Paper,
	SxProps,
	Theme,
	Typography
} from '@mui/material';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';


export default function SpamRateScore(props: {
	score: number | null,
	verdict: string | null,
	suggestions: Array<string>,
	sx?: SxProps<Theme>,
}) {

	return (
		<Paper variant={"outlined"} sx={{p: 3, borderRadius: 2}}>
			<Box display={"flex"} flexDirection={"row"} justifyContent={"space-between"}>
				<Typography variant="h5" component="div" sx={{fontWeight: 'bold', mb: 2}}>
					Score: {props.score !== null ? `${props.score}%` : "---"}
				</Typography>

				{/*<Box sx={{display: 'flex', alignItems: 'center', mb: 2}}>*/}
				{/*	<Typography variant="h6" component="div" sx={{mr: 1}}>*/}
				{/*		Verdict:*/}
				{/*	</Typography>*/}
				{/*	<Box sx={{display: 'flex', alignItems: 'center'}}>*/}
				{/*		<Typography variant="h6" component="div">*/}
				{/*			{props.verdict || "---"}*/}
				{/*		</Typography>*/}
				{/*	</Box>*/}
				{/*</Box>*/}
			</Box>

			<LinearProgress
				variant="determinate"
				value={props.score !== null ? props.score : 0}
				sx={{
					height: 12,
					borderRadius: 6,
					backgroundColor: (theme) => {
						if (theme.palette.mode === "dark") {
							if (props.score === null) {
								return theme.palette.grey["800"];
							} else if (props.score < 75) {
								return darken(theme.palette.error.dark, 0.5);
							} else if (props.score < 85) {
								return darken(theme.palette.warning.dark, 0.5);
							} else {
								return darken(theme.palette.success.dark, 0.5);
							}
						} else {
							if (props.score === null) {
								return theme.palette.grey["200"];
							} else if (props.score < 75) {
								return lighten(theme.palette.error.light, 0.5);
							} else if (props.score < 85) {
								return lighten(theme.palette.warning.light, 0.5);
							} else {
								return lighten(theme.palette.success.light, 0.5);
							}
						}
					},
					mb: 2,
					'& .MuiLinearProgress-bar': {
						backgroundColor: (theme) => {
							if (props.score === null) {
								return theme.palette.grey["200"];
							} else if (props.score < 75) {
								return theme.palette.error.main;
							} else if (props.score < 85) {
								return theme.palette.warning.main;
							} else {
								return theme.palette.success.main;
							}
						},
						borderRadius: 6,
					}
				}}
			/>

			{/*<Box sx={{display: 'flex', alignItems: 'center', mb: 2}}>*/}
			{/*	<Typography variant="h6" component="div" sx={{mr: 1}}>*/}
			{/*		Verdict:*/}
			{/*	</Typography>*/}
			{/*	<Box sx={{display: 'flex', alignItems: 'center'}}>*/}
			{/*		<Typography variant="h6" component="div">*/}
			{/*			{props.verdict || "---"}*/}
			{/*		</Typography>*/}
			{/*	</Box>*/}
			{/*</Box>*/}

			<Typography variant="h6" component="div" sx={{mb: 1}}>
				Suggestions:
			</Typography>

			<List disablePadding>
				{props.suggestions.map((suggestion, index) => (
					<ListItem key={index} disablePadding sx={{py: 0.5}}>
						<ListItemIcon sx={{minWidth: 24}}>
							<FiberManualRecordIcon sx={{fontSize: 12}}/>
						</ListItemIcon>
						<ListItemText primary={suggestion}/>
					</ListItem>
				))}
			</List>
		</Paper>
	);
};
