import { useEffect, useCallback, useRef } from "react";
import { useBlocker, useBeforeUnload } from "react-router-dom";

export function useNavigateBlocker(
  when: boolean,
  confirmFn: () => Promise<boolean>
) {
  const blocker = useBlocker(when);
  const handlingBlock = useRef(false); 

  useEffect(() => {
    if (blocker.state === "blocked" && !handlingBlock.current) {
      handlingBlock.current = true; 

      confirmFn().then((allow) => {
        if (allow) {
          blocker.proceed();
        } else {
          blocker.reset();
        }
        handlingBlock.current = false;
      });
    }
  }, [blocker, confirmFn]);

  useBeforeUnload(
    useCallback(
      (event: BeforeUnloadEvent) => {
        if (when) {
          event.preventDefault();
          event.returnValue = "You have unsaved changes.";
        }
      },
      [when]
    ),
    { capture: true }
  );
}
