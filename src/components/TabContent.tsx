import {Box} from "@mui/material";
import {ReactNode} from "react";

interface TabContentProps {
  children?: ReactNode;
  index: number;
  value: number;
}

export default function TabContent(props: TabContentProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabcontent-${index}`}
      aria-labelledby={`tabcontent-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 6}}>{children}</Box>}
    </div>
  );
}
