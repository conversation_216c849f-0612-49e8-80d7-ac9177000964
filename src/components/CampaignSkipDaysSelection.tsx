import React, {CSSProperties} from "react";
import {Stack, ToggleButton, ToggleButtonGroup, Typography, useTheme} from "@mui/material";


export default function CampaignSkipDaysSelection(props: {
	skipDays: Array<string>
	onChange: (selectedDays: Array<string>) => void,
	style?: CSSProperties,
}) {

	const theme = useTheme();

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<Stack direction={"column"} spacing={3} sx={props.style}>
			<Stack direction={"column"}>
				<Typography variant={"subtitle1"} sx={{fontWeight: "bold"}}>
					Select the days to send your email campaign on:
				</Typography>
				<Typography variant={"body2"}>
					We will send emails only on the below selected days.
				</Typography>
			</Stack>
			<ToggleButtonGroup
				color={"primary"}
				value={props.skipDays}
				onChange={(_e, v) => {
					console.log(v)
					props.onChange(v)
				}}
				sx={{
					'&.Mui-selected': {
						color: "white"
					}
				}}
			>
				<ToggleButton size={"small"} value="Sunday" sx={{
					'&.Mui-selected': {
						color: theme.palette.mode === "dark" ? "black" : "white",
						backgroundColor: "primary.main",
					}
				}}>
					SUN
				</ToggleButton>
				<ToggleButton size={"small"} value="Monday" sx={{
					'&.Mui-selected': {
						color: theme.palette.mode === "dark" ? "black" : "white",
						backgroundColor: "primary.main",
					}
				}}>
					MON
				</ToggleButton>
				<ToggleButton size={"small"} value="Tuesday" sx={{
					'&.Mui-selected': {
						color: theme.palette.mode === "dark" ? "black" : "white",
						backgroundColor: "primary.main",
					}
				}}>
					TUE
				</ToggleButton>
				<ToggleButton size={"small"} value="Wednesday" sx={{
					'&.Mui-selected': {
						color: theme.palette.mode === "dark" ? "black" : "white",
						backgroundColor: "primary.main",
					}
				}}>
					WED
				</ToggleButton>
				<ToggleButton size={"small"} value="Thursday" sx={{
					'&.Mui-selected': {
						color: theme.palette.mode === "dark" ? "black" : "white",
						backgroundColor: "primary.main",
					}
				}}>
					THU
				</ToggleButton>
				<ToggleButton size={"small"} value="Friday" sx={{
					'&.Mui-selected': {
						color: theme.palette.mode === "dark" ? "black" : "white",
						backgroundColor: "primary.main",
					}
				}}>
					FRI
				</ToggleButton>
				<ToggleButton size={"small"} value="Saturday" sx={{
					'&.Mui-selected': {
						color: theme.palette.mode === "dark" ? "black" : "white",
						backgroundColor: "primary.main",
					}
				}}>
					SAT
				</ToggleButton>
			</ToggleButtonGroup>
		</Stack>
	);
}
