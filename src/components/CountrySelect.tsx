import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import {useQuery} from "@tanstack/react-query";
import axios from "axios";
import {useEffect, useState} from "react";

interface Country {
	name: string
	code: string
}


export default function CountrySelect(props: {
	onCountrySelected: (name: string) => void,
}) {
	const [countries, setCountries] = useState<Country[]>([
		{name: "India", code: "IN"},
		{name: "United States of Ameria", code: "US"},
		{name: "Japan", code: "JP"},
		{name: "Germany", code: "DE"},
	]);
	const [selectedCountry, setSelectedCountry] = useState<Country>();

	const fetchCountryData = useQuery({
		queryKey: ["fetchCountryData"],
		queryFn: () => axios.get("https://restcountries.com/v3.1/all?fields=name,cca2"),
		refetchOnWindowFocus: false,
	});
	useEffect(() => {
		if (fetchCountryData.data) {
			let data = fetchCountryData.data.data;
			let mappedValues: Country[] = data.map(value => {
				return {name: value["name"]["common"], code: value["cca2"]}
			});
			mappedValues.sort((a, b) => a.name.localeCompare(b.name));
			setCountries(mappedValues);
		}
	}, [fetchCountryData.data]);

	return (
		<Autocomplete
			sx={{width: 300}}
			options={countries}
			autoHighlight
			value={selectedCountry}
			onChange={(_event, value) => {
				if (value) {
					setSelectedCountry(value);
					props.onCountrySelected(value.name);
				}
			}}
			getOptionLabel={(option) => option.name}
			renderOption={(props, option) => {
				const {key, ...optionProps} = props;
				return (
					<Box
						key={key}
						component="li"
						sx={{'& > img': {mr: 2, flexShrink: 0}}}
						{...optionProps}
					>
						<img
							loading="lazy"
							width="20"
							srcSet={`https://flagcdn.com/w40/${option.code.toLowerCase()}.png 2x`}
							src={`https://flagcdn.com/w20/${option.code.toLowerCase()}.png`}
							alt=""
						/>
						{option.name}
					</Box>
				);
			}}
			renderInput={(params) => (
				<TextField
					{...params}
					label="Choose a country"
					slotProps={{
						htmlInput: {
							...params.inputProps,
							autoComplete: 'new-password', // disable autocomplete and autofill
						},
					}}
				/>
			)}
		/>
	);
}
