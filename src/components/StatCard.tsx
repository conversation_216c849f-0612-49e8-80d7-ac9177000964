import React from 'react';
import {Box, Card, CardContent, Stack, SxProps, Theme, Typography} from '@mui/material';
import {SvgIconComponent} from '@mui/icons-material';

interface StatCardProps {
	icon: React.ReactElement<SvgIconComponent>;
	heading: string;
	value: string | number | React.ReactElement;
	color?: string;
	sx?: SxProps<Theme>;
}

export default function StatCard({icon, heading, value, color = 'primary.main', sx = {}}: StatCardProps) {
	return (
		<Card
			elevation={1}
			sx={{
				height: '100%',
				display: 'flex',
				flexDirection: 'column',
				justifyContent: 'center',
				alignItems: 'center',
				...sx
			}}
		>
			<CardContent
				sx={{
					display: 'flex',
					flexDirection: 'row',
					alignItems: 'center',
					width: '100%',
					'&:last-child': {
            paddingBottom: 2,
          },
				}}
			>
				<Box
					sx={{
						color,
						display: 'flex',
						justifyContent: 'center',
						alignItems: 'center',
						width: 48,
						height: 48,
						borderRadius: '50%',
						backgroundColor: `${color}15`,
						marginRight: 1.5,
					}}
				>
					{icon}
				</Box>

				<Stack direction={"column"}>
					<Typography
						variant="subtitle2"
						color="text.secondary"
						sx={{textAlign: 'left'}}
					>
						{heading}
					</Typography>
					<Typography
						variant="h6"
						component="div"
						sx={{fontWeight: 'bold', textAlign: 'left'}}
					>
						{value}
					</Typography>
				</Stack>
			</CardContent>
		</Card>
	);
};
