import React, {useState} from 'react';
import Alert from '@mui/material/Alert';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Button from '@mui/material/Button';
import Link from '@mui/material/Link';
import {Box, SxProps} from '@mui/material';

export default function EmailHowToInfoAlert(props: {
	sx?: SxProps
}) {
	const [variablesOpen, setVariablesOpen] = useState(false);
	const [spintaxOpen, setSpintaxOpen] = useState(false);
	const [senderNameOpen, setSenderNameOpen] = useState(false);

	const handleVariablesOpen = () => {
		setVariablesOpen(true);
	};

	const handleVariablesClose = () => {
		setVariablesOpen(false);
	};

	const handleSpintaxOpen = () => {
		setSpintaxOpen(true);
	};

	const handleSpintaxClose = () => {
		setSpintaxOpen(false);
	};

	const handleSenderNameOpen = () => {
		setSenderNameOpen(true);
	};

	const handleSenderNameClose = () => {
		setSenderNameOpen(false);
	};

	return (
		<Box sx={props.sx}>
			<Alert severity="info">
				Click the links to learn how to add
				{" "}<Link component={"button"} onClick={() => handleVariablesOpen()}>Variables</Link>,{" "}
				{" "}<Link component={"button"} onClick={() => handleSpintaxOpen()}>Spintax</Link>{" "}
				and
				{" "}<Link component={"button"} onClick={() => handleSenderNameOpen()}>Sender Name</Link>{" "}
				to your emails.
			</Alert>

			{/* Variables Dialog */}
			<Dialog
				open={variablesOpen}
				onClose={handleVariablesClose}
				aria-labelledby="variables-dialog-title"
				aria-describedby="variables-dialog-description"
			>
				<DialogTitle id="variables-dialog-title">Adding Variables</DialogTitle>
				<DialogContent>
					<DialogContentText id="variables-dialog-description">
						Variables are placeholders replaced by data from your contact list.
						You can add variables in your email Body and Subject by enclosing the variable name with double
						curly-brackets. Variable names are the <b>column names</b> in your uploaded CSV file.
						<br/><br/>
						For example, if you have a column named "First Name" then use it as<br/> {"{{First Name}}"}.
					</DialogContentText>
				</DialogContent>
				<DialogActions>
					<Button onClick={handleVariablesClose} color="primary">
						Close
					</Button>
				</DialogActions>
			</Dialog>

			{/* Spintax Dialog */}
			<Dialog
				open={spintaxOpen}
				onClose={handleSpintaxClose}
				aria-labelledby="spintax-dialog-title"
				aria-describedby="spintax-dialog-description"
			>
				<DialogTitle id="spintax-dialog-title">Adding Spintax</DialogTitle>
				<DialogContent>
					<DialogContentText id="spintax-dialog-description">
						Spintax is a <b>random combination of words and phrases</b> to create variations of the same email. You can
						add
						spintax into your email Subject and Body by following the below syntax:<br/>
						{"{word1|word2|word3}"}<br/>
						<br/>
						{"{"} and {"}"} are the opening and closing tags for the spintax. Words/phrases to be used in generating
						variations are to be separated by a pipe "|" character. The above example shows 3 words but you can use any
						number of words and phrases you want.
						<br/>
						<br/>
						Example Usage:<br/>
						{"Hope you have {an amazing|a very good|a great} day!"}
					</DialogContentText>
				</DialogContent>
				<DialogActions>
					<Button onClick={handleSpintaxClose} color="primary">
						Close
					</Button>
				</DialogActions>
			</Dialog>

			{/* Sender Name Dialog */}
			<Dialog
				open={senderNameOpen}
				onClose={handleSenderNameClose}
				aria-labelledby="sender-name-title"
				aria-describedby="sender-name-dialog-description"
			>
				<DialogTitle id="sender-name-title">Adding Sender Name</DialogTitle>
				<DialogContent>
					<DialogContentText id="sender-name-dialog-description">
						You can easily add sender names by using [sender] wherever required in the email message. We'll substitute
						it with the correct name based on the email being used for sending this message.
					</DialogContentText>
				</DialogContent>
				<DialogActions>
					<Button onClick={handleSenderNameClose} color="primary">
						Close
					</Button>
				</DialogActions>
			</Dialog>
		</Box>
	);
}
