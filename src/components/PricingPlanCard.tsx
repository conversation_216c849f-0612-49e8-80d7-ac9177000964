import React, {useEffect, useState} from 'react';
import {loadStripe, Stripe, StripeError} from "@stripe/stripe-js";
import {Box, Button, Card, CardContent, List, ListItem, ListItemText, Typography, useTheme} from '@mui/material';
import {useMutation} from "@tanstack/react-query";
import {ApiRequestFailed, authenticateAndPostData, retryFn} from "@lib/apis";
import SubscriptionChangeModal from "@components/SubscriptionChangeModal";
import {useNavigate} from "react-router-dom";
import {urls} from "@routes";
import {useSnackbar} from "notistack";

interface PricingPlanCardProps {
	planId: number
	planName: string;
	isMonthly: boolean;
	monthlyPrice: number;
	annualPrice: number;
	monthlyFeatureList: string[];
	annualFeatureList: string[];
	isPopular?: boolean;
	currentPlan?: boolean;
	paidUser?: boolean;
}


export default function PricingPlanCard({
																					planId,
																					planName,
																					isMonthly,
																					monthlyPrice,
																					annualPrice,
																					monthlyFeatureList,
																					annualFeatureList,
																					isPopular = false,
																					currentPlan = false,
																					paidUser = false,
																				}: PricingPlanCardProps) {
	const navigate = useNavigate();
	const theme = useTheme();
	const {enqueueSnackbar} = useSnackbar();

	const [
		openPlanChangeModal,
		setOpenPlanChangeModal
	] = useState<boolean>(false);
	const [
		stripe,
		setStripe
	] = useState<Stripe | null>(null);

	// Load stripe.
	useEffect(() => {
		loadStripe(
			process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || ""
		).then(value => {
			setStripe(value);
		});
	}, []);

	// Mutation for purchasing plan + special handling for signup plan selection "free plan".
	const purchasePlanMutation = useMutation({
		mutationKey: ["purchasePlan", planName, isMonthly],
		mutationFn: () => authenticateAndPostData("/start-checkout/", {
			plan_id: planId,
			billing_period: isMonthly ? "monthly" : "annual",
			affiliate_id: window["affiliateId"],
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: (response) => {
			if (response.data["is_free_plan"]) {
				navigate(urls["dashboard"]);
			} else {
				window.location.href = response.data["session_url"];
			}
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
		}
	});

	// Mutation for changing plan.
	const changePlanMutation = useMutation({
		mutationKey: ["changePlan", planId, isMonthly],
		mutationFn: () => authenticateAndPostData("/change-subscription/", {
			plan_id: planId,
			billing_period: isMonthly ? "monthly" : "annual",
		}),
		gcTime: 0,
		retry: retryFn,
		onSuccess: (response) => {
			if (response.data["additonal_action"]) {
				let clientSecret: string = response.data["client_secret"];
				if (stripe) {
					stripe.confirmPayment({
						clientSecret: clientSecret,
						confirmParams: {
							return_url: process.env.REACT_APP_FRONTEND + urls["checkoutSuccess"] + `?sub_id=${response.data["sub_id"]}`
						}

					}).then(({error}: { error: StripeError }) => {
						setOpenPlanChangeModal(false);
						if ((error.type === "card_error") || (error.type === "validation_error")) {
							// In case of card or validation error, show error toast to user.
							enqueueSnackbar(error.message, {
								variant: "error",
							});

						} else if (error.type === "invalid_request_error") {
							// This could be due to an invalid request or 3DS authentication failures.
							enqueueSnackbar("Failed to process payment: Invalid request or 3DS authentication failure.", {
								variant: "error",
							});

						} else {
							// In case of any other error, log to console and show toast.
							console.error(error.type);
							console.error(error.message);
							enqueueSnackbar(`Failed to process payment: ${error.message || "N/A"}`, {
								variant: "error",
							});
						}
					})
				} else {
					setOpenPlanChangeModal(false);
					enqueueSnackbar("Failed to load Stripe.", {
						variant: "error",
					});
					console.error("Failed to load Stripe.js");
				}

			} else {
				console.log("payment successful!");
				navigate(urls["checkoutSuccess"]);
			}
		},
		onError: (error: ApiRequestFailed) => {
			console.error(error);
			setOpenPlanChangeModal(false);
		}
	});

	function purchasePlanHandler() {
		if (!currentPlan) {
			if (paidUser) {
				setOpenPlanChangeModal(true);
			} else {
				purchasePlanMutation.mutate();
			}
		}
	}

	// ============================================================
	// --------------------- MAIN RENDER CODE ---------------------
	// ============================================================

	return (
		<Card
			sx={{
				minHeight: 400,
				display: 'flex',
				flexDirection: 'column',
				borderRadius: 2,
				boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
				position: 'relative',
				'&:hover': {
					boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
				}
			}}
		>
			<CardContent sx={{
				textAlign: 'center',
				p: 3,
				display: 'flex',
				flexDirection: 'column',
				height: '100%'
			}}>
				<Typography
					variant="h5"
					component="h3"
					color={"primary"}
					sx={{
						fontWeight: 700,
						mb: 3
					}}
				>
					{planName}
				</Typography>

				<Box sx={{mb: 3}}>
					<Typography
						variant="h3"
						component="div"
						sx={{
							fontWeight: 400,
							display: 'flex',
							alignItems: 'baseline',
							justifyContent: 'center',
							gap: 0.5
						}}
					>
						${isMonthly ? monthlyPrice : annualPrice}
						<Typography
							variant="body1"
							component="span"
							sx={{
								color: theme.palette.mode === "dark" ? "#b0b0b0" : "#858484",
								fontWeight: 400,
								fontSize: '1rem'
							}}
						>
							{isMonthly ? "/ month" : "/ year"}
						</Typography>
					</Typography>
				</Box>

				<List sx={{
					flex: 1,
					width: '100%',
					p: 0,
					mb: 2
				}}>
					{(isMonthly ? monthlyFeatureList : annualFeatureList).map((feature, index) => (
						<ListItem
							key={index}
							sx={{
								px: 0,
								py: 0.5,
								justifyContent: 'center'
							}}
						>
							<ListItemText
								primary={feature}
								sx={{
									textAlign: 'center',
									'& .MuiListItemText-primary': {
										color: theme.palette.mode === "dark" ? "#b0b0b0" : "#858484",
										fontSize: '0.95rem'
									}
								}}
							/>
						</ListItem>
					))}
				</List>

				{/* Purchase Plan Button */}
				<Button
					variant={isPopular ? "contained" : "outlined"}
					disabled={currentPlan || purchasePlanMutation.isPending}
					onClick={purchasePlanHandler}
					sx={{
						mt: 'auto',
						py: 1.5,
						px: 4,
						borderRadius: 1,
						textTransform: 'uppercase',
						fontWeight: 600,
						fontSize: '0.85rem',
						letterSpacing: '0.5px',
					}}
				>
					{currentPlan ? "Current Plan" : (paidUser ? "Switch Plan" : "Select Plan")}
				</Button>
			</CardContent>
			<SubscriptionChangeModal planName={planName}
															 price={isMonthly ? monthlyPrice : annualPrice}
															 billingPeriod={isMonthly ? "month" : "year"}
															 open={openPlanChangeModal}
															 onClose={() => setOpenPlanChangeModal(false)}
															 onConfirm={() => {
																 changePlanMutation.mutate();
															 }}/>
		</Card>
	);
};
